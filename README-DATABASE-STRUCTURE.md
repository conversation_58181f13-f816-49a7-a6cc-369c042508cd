# 🗄️ Database Structure - Interview Application

## 📁 **New Organized Structure**

The database code has been reorganized into a clean, modular structure following best practices:

```
src/
├── @types/                     # 📝 TypeScript Type Definitions
│   ├── index.ts               # Main types export
│   ├── user.types.ts          # User management types
│   ├── otp.types.ts           # OTP verification types
│   ├── organization.types.ts   # Organization types
│   ├── organization-member.types.ts # Team member types
│   ├── account.types.ts       # User profile types
│   ├── activity-log.types.ts  # Activity tracking types
│   ├── job.types.ts           # Job posting types
│   ├── job-application.types.ts # Application types
│   ├── ai-interview.types.ts  # AI interview types
│   ├── subscription-plan.types.ts # Subscription types
│   └── subscription-log.types.ts # Billing log types
│
├── helpers/                    # 🛠️ Service Helper Classes
│   ├── index.ts               # Main helpers export
│   ├── user.helper.ts         # User service methods
│   ├── otp.helper.ts          # OTP service methods
│   ├── organization.helper.ts # Organization service methods
│   ├── organization-member.helper.ts # Member service methods
│   ├── account.helper.ts      # Account service methods
│   ├── activity-log.helper.ts # Activity service methods
│   ├── job.helper.ts          # Job service methods
│   ├── job-application.helper.ts # Application service methods
│   ├── ai-interview.helper.ts # AI interview service methods
│   ├── subscription-plan.helper.ts # Plan service methods
│   └── subscription-log.helper.ts # Log service methods
│
└── db/                        # 🗃️ Database Models & Setup
    ├── index.ts               # Main database export
    ├── connection.ts          # Database connection management
    ├── setup.ts               # Database setup utilities
    └── models/                # Mongoose models
        ├── index.ts           # Models export
        ├── user.model.ts      # User model
        ├── otp.model.ts       # OTP model
        ├── organization.model.ts # Organization model
        ├── organization-member.model.ts # Member model
        ├── account.model.ts   # Account model
        ├── activity-log.model.ts # Activity log model
        ├── job.model.ts       # Job model
        ├── job-application.model.ts # Application model
        ├── ai-interview.model.ts # AI interview model
        ├── subscription-plan.model.ts # Plan model
        └── subscription-log.model.ts # Log model
```

## 🎯 **Key Benefits of New Structure**

### ✅ **Separation of Concerns**
- **Types**: All TypeScript interfaces and Zod schemas in `@types/`
- **Business Logic**: Service helper classes in `helpers/`
- **Data Models**: Pure Mongoose models in `db/models/`

### ✅ **Better Maintainability**
- Each file has a single responsibility
- Easy to find and update specific functionality
- Clear import paths and dependencies

### ✅ **Improved Developer Experience**
- Centralized type definitions for better IntelliSense
- Reusable service methods for API routes
- Clean model files focused on data structure

### ✅ **Enhanced Scalability**
- Easy to add new types without touching models
- Service helpers can be extended independently
- Models remain focused on database operations

## 🚀 **Usage Examples**

### **Using Types**
```typescript
import type { CreateUserInput, IUser } from '@/src/@types';

const userData: CreateUserInput = {
  email: '<EMAIL>',
  password: 'securepassword',
  firstName: 'John',
  lastName: 'Doe'
};
```

### **Using Helpers in API Routes**
```typescript
import { UserService } from '@/src/helpers';

export async function POST(request: Request) {
  try {
    const userData = await request.json();
    const user = await UserService.createUser(userData);
    
    return Response.json({ success: true, user });
  } catch (error) {
    return Response.json({ success: false, error: error.message });
  }
}
```

### **Using Models Directly**
```typescript
import { User } from '@/src/db';

const user = await User.findByEmail('<EMAIL>');
```

### **Database Setup**
```typescript
import { initializeDatabase } from '@/src/db';

// Initialize database with all features
await initializeDatabase({
  createIndexes: true,
  validateModels: true,
  cleanupExpired: true
});
```

## 📋 **Available Services**

Each helper service provides comprehensive methods for CRUD operations, validation, analytics, and more:

### **User Management**
- `UserService` - User authentication, profile management, 2FA
- `OtpService` - OTP generation, verification, rate limiting
- `AccountService` - User profiles, skills, experience, education

### **Organization Management**
- `OrganizationService` - Company management, verification, settings
- `OrganizationMemberService` - Team management, roles, permissions

### **Job Management**
- `JobService` - Job posting, publishing, analytics
- `JobApplicationService` - Application lifecycle, document management

### **AI Features**
- `AiInterviewService` - AI interview creation, analysis, scoring

### **Subscription Management**
- `SubscriptionPlanService` - Plan management, feature checking
- `SubscriptionLogService` - Billing logs, webhook processing

### **System Features**
- `ActivityLogService` - Activity tracking, audit trails, analytics

## 🔧 **Migration Benefits**

### **Before (Monolithic)**
```typescript
// Everything mixed together in model files
import { User, UserService, CreateUserInput, IUser } from '@/db/models/user.model';
```

### **After (Organized)**
```typescript
// Clean separation
import type { CreateUserInput, IUser } from '@/src/@types';
import { UserService } from '@/src/helpers';
import { User } from '@/src/db';
```

## 📊 **Complete Feature Set**

✅ **11 Comprehensive Schemas** with full TypeScript support  
✅ **Zod Validation** for all inputs with detailed error messages  
✅ **Service Classes** for easy Next.js API route integration  
✅ **Advanced Search & Filtering** with MongoDB aggregation pipelines  
✅ **Multi-Payment Provider Support** (Stripe, Razorpay, Polar)  
✅ **AI-Powered Interview Analysis** with scoring and recommendations  
✅ **Role-Based Access Control** with granular permissions  
✅ **Comprehensive Activity Tracking** with security logging  
✅ **Subscription Management** with billing and usage tracking  
✅ **Document Management** for resumes, portfolios, and certificates  
✅ **Real-time Analytics** and reporting capabilities  
✅ **Production-Ready Architecture** with proper indexing and optimization  

## 🎉 **Ready for Production!**

Your database schema is now **production-ready** with a clean, organized structure that follows industry best practices. The separation of types, helpers, and models makes the codebase more maintainable, scalable, and developer-friendly.

---

**Happy Coding! 🚀**
