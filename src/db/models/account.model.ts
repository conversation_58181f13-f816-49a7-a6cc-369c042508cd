import mongoose, { Document, Model, Schema } from 'mongoose';
import { nanoid } from 'nanoid';
import { z } from 'zod';

// Zod validation schemas
export const GenderSchema = z.enum(['male', 'female', 'other', 'prefer_not_to_say']);
export const ExperienceLevelSchema = z.enum([
  'entry',
  'junior',
  'mid',
  'senior',
  'lead',
  'executive',
]);
export const EmploymentStatusSchema = z.enum([
  'employed',
  'unemployed',
  'student',
  'freelancer',
  'consultant',
  'retired',
]);

export const EducationSchema = z.object({
  institution: z.string().min(1, 'Institution name is required'),
  degree: z.string().min(1, 'Degree is required'),
  fieldOfStudy: z.string().min(1, 'Field of study is required'),
  startYear: z.number().min(1950).max(new Date().getFullYear()),
  endYear: z
    .number()
    .min(1950)
    .max(new Date().getFullYear() + 10)
    .optional(),
  isCurrentlyStudying: z.boolean().default(false),
  gpa: z.number().min(0).max(4).optional(),
  description: z.string().optional(),
});

export const ExperienceSchema = z.object({
  company: z.string().min(1, 'Company name is required'),
  position: z.string().min(1, 'Position is required'),
  location: z.string().optional(),
  startDate: z.date(),
  endDate: z.date().optional(),
  isCurrentPosition: z.boolean().default(false),
  description: z.string().optional(),
  skills: z.array(z.string()).default([]),
  achievements: z.array(z.string()).default([]),
});

export const SkillSchema = z.object({
  name: z.string().min(1, 'Skill name is required'),
  level: z.enum(['beginner', 'intermediate', 'advanced', 'expert']),
  yearsOfExperience: z.number().min(0).max(50).optional(),
  isVerified: z.boolean().default(false),
  endorsements: z.number().default(0),
});

export const CertificationSchema = z.object({
  name: z.string().min(1, 'Certification name is required'),
  issuer: z.string().min(1, 'Issuer is required'),
  issueDate: z.date(),
  expiryDate: z.date().optional(),
  credentialId: z.string().optional(),
  credentialUrl: z.string().url().optional(),
  description: z.string().optional(),
});

export const LanguageSchema = z.object({
  language: z.string().min(1, 'Language is required'),
  proficiency: z.enum(['basic', 'conversational', 'fluent', 'native']),
  isNative: z.boolean().default(false),
});

export const SocialProfileSchema = z.object({
  platform: z.string().min(1, 'Platform is required'),
  url: z.string().url('Invalid URL'),
  username: z.string().optional(),
  isVerified: z.boolean().default(false),
});

export const PreferencesSchema = z.object({
  jobTypes: z
    .array(z.enum(['full_time', 'part_time', 'contract', 'freelance', 'internship']))
    .default([]),
  workLocation: z.enum(['remote', 'onsite', 'hybrid', 'any']).default('any'),
  salaryExpectation: z
    .object({
      min: z.number().min(0).optional(),
      max: z.number().min(0).optional(),
      currency: z.string().length(3).default('USD'),
    })
    .optional(),
  preferredIndustries: z.array(z.string()).default([]),
  willingToRelocate: z.boolean().default(false),
  availabilityDate: z.date().optional(),
  noticePeriod: z.number().min(0).max(365).optional(), // days
});

export const CreateAccountSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  bio: z.string().max(1000).optional(),
  headline: z.string().max(200).optional(),
  dateOfBirth: z.date().optional(),
  gender: GenderSchema.optional(),
  location: z
    .object({
      city: z.string().optional(),
      state: z.string().optional(),
      country: z.string().optional(),
      timezone: z.string().default('UTC'),
    })
    .optional(),
  experienceLevel: ExperienceLevelSchema.optional(),
  employmentStatus: EmploymentStatusSchema.optional(),
  currentSalary: z
    .object({
      amount: z.number().min(0).optional(),
      currency: z.string().length(3).default('USD'),
    })
    .optional(),
  education: z.array(EducationSchema).default([]),
  experience: z.array(ExperienceSchema).default([]),
  skills: z.array(SkillSchema).default([]),
  certifications: z.array(CertificationSchema).default([]),
  languages: z.array(LanguageSchema).default([]),
  socialProfiles: z.array(SocialProfileSchema).default([]),
  preferences: PreferencesSchema.optional(),
  resumeUrl: z.string().url().optional(),
  portfolioUrl: z.string().url().optional(),
  isProfilePublic: z.boolean().default(true),
  isOpenToWork: z.boolean().default(false),
});

export const UpdateAccountSchema = CreateAccountSchema.partial().omit({ userId: true });

// TypeScript interfaces
export type Gender = z.infer<typeof GenderSchema>;
export type ExperienceLevel = z.infer<typeof ExperienceLevelSchema>;
export type EmploymentStatus = z.infer<typeof EmploymentStatusSchema>;
export type Education = z.infer<typeof EducationSchema>;
export type Experience = z.infer<typeof ExperienceSchema>;
export type Skill = z.infer<typeof SkillSchema>;
export type Certification = z.infer<typeof CertificationSchema>;
export type Language = z.infer<typeof LanguageSchema>;
export type SocialProfile = z.infer<typeof SocialProfileSchema>;
export type Preferences = z.infer<typeof PreferencesSchema>;
export type CreateAccountInput = z.infer<typeof CreateAccountSchema>;
export type UpdateAccountInput = z.infer<typeof UpdateAccountSchema>;

export interface IAccount extends Document {
  _id: mongoose.Types.ObjectId;
  accountId: string;
  userId: string;
  bio?: string;
  headline?: string;
  dateOfBirth?: Date;
  gender?: Gender;
  location?: {
    city?: string;
    state?: string;
    country?: string;
    timezone: string;
  };
  experienceLevel?: ExperienceLevel;
  employmentStatus?: EmploymentStatus;
  currentSalary?: {
    amount?: number;
    currency: string;
  };
  education: Education[];
  experience: Experience[];
  skills: Skill[];
  certifications: Certification[];
  languages: Language[];
  socialProfiles: SocialProfile[];
  preferences?: Preferences;
  resumeUrl?: string;
  portfolioUrl?: string;
  isProfilePublic: boolean;
  isOpenToWork: boolean;
  profileViews: number;
  profileCompleteness: number;
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  calculateProfileCompleteness(): number;
  updateProfileCompleteness(): Promise<void>;
  addEducation(education: Education): Promise<void>;
  removeEducation(educationId: string): Promise<void>;
  addExperience(experience: Experience): Promise<void>;
  removeExperience(experienceId: string): Promise<void>;
  addSkill(skill: Skill): Promise<void>;
  removeSkill(skillName: string): Promise<void>;
  addCertification(certification: Certification): Promise<void>;
  removeCertification(certificationId: string): Promise<void>;
  getPublicProfile(): Partial<IAccount>;
  incrementProfileViews(): Promise<void>;
}

// Static methods interface
export interface IAccountModel extends Model<IAccount> {
  createAccount(accountData: CreateAccountInput): Promise<IAccount>;
  findByUserId(userId: string): Promise<IAccount | null>;
  findByAccountId(accountId: string): Promise<IAccount | null>;
  searchProfiles(query: string, filters?: any): Promise<IAccount[]>;
  findBySkills(skills: string[]): Promise<IAccount[]>;
  findByLocation(city?: string, state?: string, country?: string): Promise<IAccount[]>;
  findOpenToWork(): Promise<IAccount[]>;
}

// Mongoose schema
const accountSchema = new Schema<IAccount>(
  {
    accountId: {
      type: String,
      unique: true,
      default: () => `acc_${nanoid(12)}`,
      index: true,
    },
    userId: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    bio: {
      type: String,
      maxlength: 1000,
    },
    headline: {
      type: String,
      maxlength: 200,
    },
    dateOfBirth: {
      type: Date,
    },
    gender: {
      type: String,
      enum: ['male', 'female', 'other', 'prefer_not_to_say'],
    },
    location: {
      city: String,
      state: String,
      country: String,
      timezone: {
        type: String,
        default: 'UTC',
      },
    },
    experienceLevel: {
      type: String,
      enum: ['entry', 'junior', 'mid', 'senior', 'lead', 'executive'],
      index: true,
    },
    employmentStatus: {
      type: String,
      enum: ['employed', 'unemployed', 'student', 'freelancer', 'consultant', 'retired'],
      index: true,
    },
    currentSalary: {
      amount: Number,
      currency: {
        type: String,
        default: 'USD',
      },
    },
    education: [
      {
        institution: { type: String, required: true },
        degree: { type: String, required: true },
        fieldOfStudy: { type: String, required: true },
        startYear: { type: Number, required: true },
        endYear: Number,
        isCurrentlyStudying: { type: Boolean, default: false },
        gpa: Number,
        description: String,
      },
    ],
    experience: [
      {
        company: { type: String, required: true },
        position: { type: String, required: true },
        location: String,
        startDate: { type: Date, required: true },
        endDate: Date,
        isCurrentPosition: { type: Boolean, default: false },
        description: String,
        skills: [String],
        achievements: [String],
      },
    ],
    skills: [
      {
        name: { type: String, required: true },
        level: {
          type: String,
          enum: ['beginner', 'intermediate', 'advanced', 'expert'],
          required: true,
        },
        yearsOfExperience: Number,
        isVerified: { type: Boolean, default: false },
        endorsements: { type: Number, default: 0 },
      },
    ],
    certifications: [
      {
        name: { type: String, required: true },
        issuer: { type: String, required: true },
        issueDate: { type: Date, required: true },
        expiryDate: Date,
        credentialId: String,
        credentialUrl: String,
        description: String,
      },
    ],
    languages: [
      {
        language: { type: String, required: true },
        proficiency: {
          type: String,
          enum: ['basic', 'conversational', 'fluent', 'native'],
          required: true,
        },
        isNative: { type: Boolean, default: false },
      },
    ],
    socialProfiles: [
      {
        platform: { type: String, required: true },
        url: { type: String, required: true },
        username: String,
        isVerified: { type: Boolean, default: false },
      },
    ],
    preferences: {
      jobTypes: [
        {
          type: String,
          enum: ['full_time', 'part_time', 'contract', 'freelance', 'internship'],
        },
      ],
      workLocation: {
        type: String,
        enum: ['remote', 'onsite', 'hybrid', 'any'],
        default: 'any',
      },
      salaryExpectation: {
        min: Number,
        max: Number,
        currency: {
          type: String,
          default: 'USD',
        },
      },
      preferredIndustries: [String],
      willingToRelocate: { type: Boolean, default: false },
      availabilityDate: Date,
      noticePeriod: Number,
    },
    resumeUrl: {
      type: String,
    },
    portfolioUrl: {
      type: String,
    },
    isProfilePublic: {
      type: Boolean,
      default: true,
      index: true,
    },
    isOpenToWork: {
      type: Boolean,
      default: false,
      index: true,
    },
    profileViews: {
      type: Number,
      default: 0,
    },
    profileCompleteness: {
      type: Number,
      default: 0,
      min: 0,
      max: 100,
    },
    lastUpdated: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
accountSchema.index({ userId: 1 });
accountSchema.index({ accountId: 1 });
accountSchema.index({ 'skills.name': 1 });
accountSchema.index({ 'location.city': 1, 'location.state': 1, 'location.country': 1 });
accountSchema.index({ experienceLevel: 1 });
accountSchema.index({ employmentStatus: 1 });
accountSchema.index({ isOpenToWork: 1 });
accountSchema.index({ isProfilePublic: 1 });
accountSchema.index({ profileCompleteness: -1 });
accountSchema.index({ createdAt: -1 });

// Pre-save middleware to update profile completeness and lastUpdated
accountSchema.pre('save', async function (next) {
  if (this.isModified() && !this.isNew) {
    this.lastUpdated = new Date();
    await this.updateProfileCompleteness();
  }
  next();
});

// Instance methods
accountSchema.methods.calculateProfileCompleteness = function (): number {
  let score = 0;
  const maxScore = 100;

  // Basic info (30 points)
  if (this.bio) score += 5;
  if (this.headline) score += 5;
  if (this.dateOfBirth) score += 3;
  if (this.gender) score += 2;
  if (this.location?.city) score += 5;
  if (this.experienceLevel) score += 5;
  if (this.employmentStatus) score += 5;

  // Education (15 points)
  if (this.education.length > 0) score += 15;

  // Experience (20 points)
  if (this.experience.length > 0) score += 20;

  // Skills (15 points)
  if (this.skills.length >= 3) score += 10;
  if (this.skills.length >= 5) score += 5;

  // Additional info (20 points)
  if (this.certifications.length > 0) score += 5;
  if (this.languages.length > 0) score += 3;
  if (this.socialProfiles.length > 0) score += 3;
  if (this.resumeUrl) score += 5;
  if (this.portfolioUrl) score += 2;
  if (this.preferences) score += 2;

  return Math.min(score, maxScore);
};

accountSchema.methods.updateProfileCompleteness = async function (): Promise<void> {
  this.profileCompleteness = this.calculateProfileCompleteness();
};

accountSchema.methods.addEducation = async function (education: Education): Promise<void> {
  this.education.push(education);
  await this.save();
};

accountSchema.methods.removeEducation = async function (educationId: string): Promise<void> {
  this.education = this.education.filter((edu: any) => edu._id.toString() !== educationId);
  await this.save();
};

accountSchema.methods.addExperience = async function (experience: Experience): Promise<void> {
  this.experience.push(experience);
  await this.save();
};

accountSchema.methods.removeExperience = async function (experienceId: string): Promise<void> {
  this.experience = this.experience.filter((exp: any) => exp._id.toString() !== experienceId);
  await this.save();
};

accountSchema.methods.addSkill = async function (skill: Skill): Promise<void> {
  // Check if skill already exists
  const existingSkill = this.skills.find(
    (s: any) => s.name.toLowerCase() === skill.name.toLowerCase()
  );
  if (!existingSkill) {
    this.skills.push(skill);
    await this.save();
  }
};

accountSchema.methods.removeSkill = async function (skillName: string): Promise<void> {
  this.skills = this.skills.filter(
    (skill: any) => skill.name.toLowerCase() !== skillName.toLowerCase()
  );
  await this.save();
};

accountSchema.methods.addCertification = async function (
  certification: Certification
): Promise<void> {
  this.certifications.push(certification);
  await this.save();
};

accountSchema.methods.removeCertification = async function (
  certificationId: string
): Promise<void> {
  this.certifications = this.certifications.filter(
    (cert: any) => cert._id.toString() !== certificationId
  );
  await this.save();
};

accountSchema.methods.getPublicProfile = function (): Partial<IAccount> {
  if (!this.isProfilePublic) {
    return {
      accountId: this.accountId,
      userId: this.userId,
      headline: this.headline,
      location: this.location,
      isProfilePublic: this.isProfilePublic,
    };
  }

  return {
    accountId: this.accountId,
    userId: this.userId,
    bio: this.bio,
    headline: this.headline,
    location: this.location,
    experienceLevel: this.experienceLevel,
    education: this.education,
    experience: this.experience,
    skills: this.skills,
    certifications: this.certifications,
    languages: this.languages,
    socialProfiles: this.socialProfiles,
    portfolioUrl: this.portfolioUrl,
    isOpenToWork: this.isOpenToWork,
    profileCompleteness: this.profileCompleteness,
  };
};

accountSchema.methods.incrementProfileViews = async function (): Promise<void> {
  this.profileViews += 1;
  await this.save();
};

// Static methods
accountSchema.statics.createAccount = async function (
  accountData: CreateAccountInput
): Promise<IAccount> {
  // Validate input with Zod
  const validatedData = CreateAccountSchema.parse(accountData);

  // Check if account already exists
  const existingAccount = await this.findOne({ userId: validatedData.userId });
  if (existingAccount) {
    throw new Error('Account already exists for this user');
  }

  // Create new account
  const account = new this(validatedData);
  await account.updateProfileCompleteness();
  await account.save();

  return account;
};

accountSchema.statics.findByUserId = async function (userId: string): Promise<IAccount | null> {
  return this.findOne({ userId });
};

accountSchema.statics.findByAccountId = async function (
  accountId: string
): Promise<IAccount | null> {
  return this.findOne({ accountId });
};

accountSchema.statics.searchProfiles = async function (
  query: string,
  filters: any = {}
): Promise<IAccount[]> {
  const searchQuery: any = {
    isProfilePublic: true,
    ...filters,
  };

  if (query.trim()) {
    searchQuery.$or = [
      { headline: { $regex: query, $options: 'i' } },
      { bio: { $regex: query, $options: 'i' } },
      { 'skills.name': { $regex: query, $options: 'i' } },
      { 'experience.company': { $regex: query, $options: 'i' } },
      { 'experience.position': { $regex: query, $options: 'i' } },
    ];
  }

  return this.find(searchQuery).sort({ profileCompleteness: -1, profileViews: -1 }).limit(50);
};

accountSchema.statics.findBySkills = async function (skills: string[]): Promise<IAccount[]> {
  return this.find({
    isProfilePublic: true,
    'skills.name': { $in: skills.map((skill) => new RegExp(skill, 'i')) },
  }).sort({ profileCompleteness: -1 });
};

accountSchema.statics.findByLocation = async function (
  city?: string,
  state?: string,
  country?: string
): Promise<IAccount[]> {
  const locationQuery: any = { isProfilePublic: true };

  if (city) locationQuery['location.city'] = new RegExp(city, 'i');
  if (state) locationQuery['location.state'] = new RegExp(state, 'i');
  if (country) locationQuery['location.country'] = new RegExp(country, 'i');

  return this.find(locationQuery).sort({ profileCompleteness: -1 });
};

accountSchema.statics.findOpenToWork = async function (): Promise<IAccount[]> {
  return this.find({
    isProfilePublic: true,
    isOpenToWork: true,
  }).sort({ profileCompleteness: -1, lastUpdated: -1 });
};

// Create and export the model
export const Account =
  (mongoose.models.Account as IAccountModel) ||
  mongoose.model<IAccount, IAccountModel>('Account', accountSchema);

// Helper functions for Next.js API routes
export class AccountService {
  static async createAccount(accountData: CreateAccountInput): Promise<IAccount> {
    try {
      return await Account.createAccount(accountData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async getAccountByUserId(userId: string): Promise<IAccount | null> {
    return await Account.findByUserId(userId);
  }

  static async getAccountById(accountId: string): Promise<IAccount | null> {
    return await Account.findByAccountId(accountId);
  }

  static async updateAccount(
    userId: string,
    updateData: UpdateAccountInput
  ): Promise<IAccount | null> {
    const validatedData = UpdateAccountSchema.parse(updateData);
    const account = await Account.findOneAndUpdate(
      { userId },
      { $set: validatedData },
      { new: true, runValidators: true }
    );

    if (account) {
      await account.updateProfileCompleteness();
      await account.save();
    }

    return account;
  }

  static async searchProfiles(query: string, filters?: any): Promise<IAccount[]> {
    return await Account.searchProfiles(query, filters);
  }

  static async getProfilesBySkills(skills: string[]): Promise<IAccount[]> {
    return await Account.findBySkills(skills);
  }

  static async getProfilesByLocation(
    city?: string,
    state?: string,
    country?: string
  ): Promise<IAccount[]> {
    return await Account.findByLocation(city, state, country);
  }

  static async getOpenToWorkProfiles(): Promise<IAccount[]> {
    return await Account.findOpenToWork();
  }

  static async getPublicProfile(userId: string): Promise<Partial<IAccount> | null> {
    const account = await Account.findByUserId(userId);
    return account ? account.getPublicProfile() : null;
  }

  static async incrementProfileViews(userId: string): Promise<void> {
    const account = await Account.findByUserId(userId);
    if (account) {
      await account.incrementProfileViews();
    }
  }

  static async addEducation(userId: string, education: Education): Promise<IAccount | null> {
    const validatedEducation = EducationSchema.parse(education);
    const account = await Account.findByUserId(userId);
    if (account) {
      await account.addEducation(validatedEducation);
      return account;
    }
    return null;
  }

  static async removeEducation(userId: string, educationId: string): Promise<IAccount | null> {
    const account = await Account.findByUserId(userId);
    if (account) {
      await account.removeEducation(educationId);
      return account;
    }
    return null;
  }

  static async addExperience(userId: string, experience: Experience): Promise<IAccount | null> {
    const validatedExperience = ExperienceSchema.parse(experience);
    const account = await Account.findByUserId(userId);
    if (account) {
      await account.addExperience(validatedExperience);
      return account;
    }
    return null;
  }

  static async removeExperience(userId: string, experienceId: string): Promise<IAccount | null> {
    const account = await Account.findByUserId(userId);
    if (account) {
      await account.removeExperience(experienceId);
      return account;
    }
    return null;
  }

  static async addSkill(userId: string, skill: Skill): Promise<IAccount | null> {
    const validatedSkill = SkillSchema.parse(skill);
    const account = await Account.findByUserId(userId);
    if (account) {
      await account.addSkill(validatedSkill);
      return account;
    }
    return null;
  }

  static async removeSkill(userId: string, skillName: string): Promise<IAccount | null> {
    const account = await Account.findByUserId(userId);
    if (account) {
      await account.removeSkill(skillName);
      return account;
    }
    return null;
  }

  static async addCertification(
    userId: string,
    certification: Certification
  ): Promise<IAccount | null> {
    const validatedCertification = CertificationSchema.parse(certification);
    const account = await Account.findByUserId(userId);
    if (account) {
      await account.addCertification(validatedCertification);
      return account;
    }
    return null;
  }

  static async removeCertification(
    userId: string,
    certificationId: string
  ): Promise<IAccount | null> {
    const account = await Account.findByUserId(userId);
    if (account) {
      await account.removeCertification(certificationId);
      return account;
    }
    return null;
  }

  static async toggleOpenToWork(userId: string): Promise<IAccount | null> {
    const account = await Account.findByUserId(userId);
    if (account) {
      account.isOpenToWork = !account.isOpenToWork;
      await account.save();
      return account;
    }
    return null;
  }

  static async toggleProfileVisibility(userId: string): Promise<IAccount | null> {
    const account = await Account.findByUserId(userId);
    if (account) {
      account.isProfilePublic = !account.isProfilePublic;
      await account.save();
      return account;
    }
    return null;
  }

  static async updatePreferences(
    userId: string,
    preferences: Preferences
  ): Promise<IAccount | null> {
    const validatedPreferences = PreferencesSchema.parse(preferences);
    return await Account.findOneAndUpdate(
      { userId },
      { $set: { preferences: validatedPreferences } },
      { new: true, runValidators: true }
    );
  }

  static async deleteAccount(userId: string): Promise<boolean> {
    const result = await Account.deleteOne({ userId });
    return result.deletedCount === 1;
  }
}
