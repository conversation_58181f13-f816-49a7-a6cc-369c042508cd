import mongoose, { Document, Model, Schema } from 'mongoose';
import { nanoid } from 'nanoid';
import { z } from 'zod';

// Zod validation schemas
export const ActivityTypeSchema = z.enum([
  // User activities
  'user_login',
  'user_logout',
  'user_register',
  'user_profile_update',
  'user_password_change',
  'user_email_verify',
  'user_password_reset',

  // Organization activities
  'organization_create',
  'organization_update',
  'organization_delete',
  'organization_member_invite',
  'organization_member_join',
  'organization_member_leave',
  'organization_member_remove',
  'organization_role_change',

  // Job activities
  'job_create',
  'job_update',
  'job_delete',
  'job_publish',
  'job_unpublish',
  'job_application_submit',
  'job_application_withdraw',
  'job_application_review',
  'job_application_status_change',

  // Interview activities
  'interview_schedule',
  'interview_reschedule',
  'interview_cancel',
  'interview_start',
  'interview_complete',
  'interview_feedback_submit',

  // AI activities
  'ai_interview_generate',
  'ai_analysis_complete',
  'ai_recommendation_generate',

  // System activities
  'system_backup',
  'system_maintenance',
  'system_error',
  'system_security_alert',

  // Billing activities
  'subscription_create',
  'subscription_update',
  'subscription_cancel',
  'payment_success',
  'payment_failed',
  'invoice_generate',
]);

export const ActivityLevelSchema = z.enum(['info', 'warning', 'error', 'critical']);
export const ActivityStatusSchema = z.enum(['pending', 'completed', 'failed', 'cancelled']);

export const CreateActivityLogSchema = z.object({
  type: ActivityTypeSchema,
  level: ActivityLevelSchema.default('info'),
  status: ActivityStatusSchema.default('completed'),
  userId: z.string().optional(),
  organizationId: z.string().optional(),
  targetUserId: z.string().optional(),
  targetResourceId: z.string().optional(),
  targetResourceType: z.string().optional(),
  action: z.string().min(1, 'Action is required'),
  description: z.string().optional(),
  metadata: z.record(z.unknown()).optional(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  location: z
    .object({
      country: z.string().optional(),
      city: z.string().optional(),
      region: z.string().optional(),
    })
    .optional(),
  sessionId: z.string().optional(),
  requestId: z.string().optional(),
});

// TypeScript interfaces
export type ActivityType = z.infer<typeof ActivityTypeSchema>;
export type ActivityLevel = z.infer<typeof ActivityLevelSchema>;
export type ActivityStatus = z.infer<typeof ActivityStatusSchema>;
export type CreateActivityLogInput = z.infer<typeof CreateActivityLogSchema>;

export interface IActivityLog extends Document {
  _id: mongoose.Types.ObjectId;
  activityId: string;
  type: ActivityType;
  level: ActivityLevel;
  status: ActivityStatus;
  userId?: string;
  organizationId?: string;
  targetUserId?: string;
  targetResourceId?: string;
  targetResourceType?: string;
  action: string;
  description?: string;
  metadata?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  location?: {
    country?: string;
    city?: string;
    region?: string;
  };
  sessionId?: string;
  requestId?: string;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  isSecurityRelated(): boolean;
  isCritical(): boolean;
  getFormattedMessage(): string;
  toSummary(): Partial<IActivityLog>;
}

// Static methods interface
export interface IActivityLogModel extends Model<IActivityLog> {
  createLog(logData: CreateActivityLogInput): Promise<IActivityLog>;
  findByUser(userId: string, limit?: number): Promise<IActivityLog[]>;
  findByOrganization(organizationId: string, limit?: number): Promise<IActivityLog[]>;
  findByType(type: ActivityType, limit?: number): Promise<IActivityLog[]>;
  findByLevel(level: ActivityLevel, limit?: number): Promise<IActivityLog[]>;
  findSecurityLogs(limit?: number): Promise<IActivityLog[]>;
  findCriticalLogs(limit?: number): Promise<IActivityLog[]>;
  findRecentLogs(hours?: number, limit?: number): Promise<IActivityLog[]>;
  getActivityStats(startDate: Date, endDate: Date): Promise<any>;
  cleanupOldLogs(daysToKeep?: number): Promise<number>;
}

// Mongoose schema
const activityLogSchema = new Schema<IActivityLog>(
  {
    activityId: {
      type: String,
      unique: true,
      default: () => `activity_${nanoid(12)}`,
      index: true,
    },
    type: {
      type: String,
      enum: [
        'user_login',
        'user_logout',
        'user_register',
        'user_profile_update',
        'user_password_change',
        'user_email_verify',
        'user_password_reset',
        'organization_create',
        'organization_update',
        'organization_delete',
        'organization_member_invite',
        'organization_member_join',
        'organization_member_leave',
        'organization_member_remove',
        'organization_role_change',
        'job_create',
        'job_update',
        'job_delete',
        'job_publish',
        'job_unpublish',
        'job_application_submit',
        'job_application_withdraw',
        'job_application_review',
        'job_application_status_change',
        'interview_schedule',
        'interview_reschedule',
        'interview_cancel',
        'interview_start',
        'interview_complete',
        'interview_feedback_submit',
        'ai_interview_generate',
        'ai_analysis_complete',
        'ai_recommendation_generate',
        'system_backup',
        'system_maintenance',
        'system_error',
        'system_security_alert',
        'subscription_create',
        'subscription_update',
        'subscription_cancel',
        'payment_success',
        'payment_failed',
        'invoice_generate',
      ],
      required: true,
      index: true,
    },
    level: {
      type: String,
      enum: ['info', 'warning', 'error', 'critical'],
      default: 'info',
      index: true,
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'cancelled'],
      default: 'completed',
      index: true,
    },
    userId: {
      type: String,
      index: true,
    },
    organizationId: {
      type: String,
      index: true,
    },
    targetUserId: {
      type: String,
      index: true,
    },
    targetResourceId: {
      type: String,
      index: true,
    },
    targetResourceType: {
      type: String,
      index: true,
    },
    action: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    ipAddress: {
      type: String,
      index: true,
    },
    userAgent: {
      type: String,
    },
    location: {
      country: String,
      city: String,
      region: String,
    },
    sessionId: {
      type: String,
      index: true,
    },
    requestId: {
      type: String,
      index: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
activityLogSchema.index({ type: 1, timestamp: -1 });
activityLogSchema.index({ userId: 1, timestamp: -1 });
activityLogSchema.index({ organizationId: 1, timestamp: -1 });
activityLogSchema.index({ level: 1, timestamp: -1 });
activityLogSchema.index({ status: 1, timestamp: -1 });
activityLogSchema.index({ timestamp: -1 });
activityLogSchema.index({ ipAddress: 1, timestamp: -1 });
activityLogSchema.index({ sessionId: 1, timestamp: -1 });

// TTL index to automatically delete old logs (keep for 1 year by default)
activityLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 365 * 24 * 60 * 60 });

// Instance methods
activityLogSchema.methods.isSecurityRelated = function (): boolean {
  const securityTypes = [
    'user_login',
    'user_logout',
    'user_password_change',
    'user_password_reset',
    'system_security_alert',
    'organization_member_invite',
    'organization_member_join',
    'organization_member_remove',
    'organization_role_change',
  ];
  return securityTypes.includes(this.type);
};

activityLogSchema.methods.isCritical = function (): boolean {
  return this.level === 'critical' || this.level === 'error';
};

activityLogSchema.methods.getFormattedMessage = function (): string {
  const timestamp = this.timestamp.toISOString();
  const level = this.level.toUpperCase();
  const type = this.type.replace(/_/g, ' ').toUpperCase();

  return `[${timestamp}] ${level}: ${type} - ${this.action}${this.description ? ` - ${this.description}` : ''}`;
};

activityLogSchema.methods.toSummary = function (): Partial<IActivityLog> {
  return {
    activityId: this.activityId,
    type: this.type,
    level: this.level,
    status: this.status,
    action: this.action,
    description: this.description,
    timestamp: this.timestamp,
    userId: this.userId,
    organizationId: this.organizationId,
  };
};

// Static methods
activityLogSchema.statics.createLog = async function (
  logData: CreateActivityLogInput
): Promise<IActivityLog> {
  // Validate input with Zod
  const validatedData = CreateActivityLogSchema.parse(logData);

  // Create new activity log
  const log = new this(validatedData);
  await log.save();

  return log;
};

activityLogSchema.statics.findByUser = async function (
  userId: string,
  limit: number = 50
): Promise<IActivityLog[]> {
  return this.find({ userId }).sort({ timestamp: -1 }).limit(limit);
};

activityLogSchema.statics.findByOrganization = async function (
  organizationId: string,
  limit: number = 50
): Promise<IActivityLog[]> {
  return this.find({ organizationId }).sort({ timestamp: -1 }).limit(limit);
};

activityLogSchema.statics.findByType = async function (
  type: ActivityType,
  limit: number = 50
): Promise<IActivityLog[]> {
  return this.find({ type }).sort({ timestamp: -1 }).limit(limit);
};

activityLogSchema.statics.findByLevel = async function (
  level: ActivityLevel,
  limit: number = 50
): Promise<IActivityLog[]> {
  return this.find({ level }).sort({ timestamp: -1 }).limit(limit);
};

activityLogSchema.statics.findSecurityLogs = async function (
  limit: number = 100
): Promise<IActivityLog[]> {
  const securityTypes = [
    'user_login',
    'user_logout',
    'user_password_change',
    'user_password_reset',
    'system_security_alert',
    'organization_member_invite',
    'organization_member_join',
    'organization_member_remove',
    'organization_role_change',
  ];

  return this.find({ type: { $in: securityTypes } })
    .sort({ timestamp: -1 })
    .limit(limit);
};

activityLogSchema.statics.findCriticalLogs = async function (
  limit: number = 100
): Promise<IActivityLog[]> {
  return this.find({ level: { $in: ['critical', 'error'] } })
    .sort({ timestamp: -1 })
    .limit(limit);
};

activityLogSchema.statics.findRecentLogs = async function (
  hours: number = 24,
  limit: number = 100
): Promise<IActivityLog[]> {
  const since = new Date(Date.now() - hours * 60 * 60 * 1000);

  return this.find({ timestamp: { $gte: since } })
    .sort({ timestamp: -1 })
    .limit(limit);
};

activityLogSchema.statics.getActivityStats = async function (
  startDate: Date,
  endDate: Date
): Promise<any> {
  const pipeline = [
    {
      $match: {
        timestamp: { $gte: startDate, $lte: endDate },
      },
    },
    {
      $group: {
        _id: {
          type: '$type',
          level: '$level',
          status: '$status',
        },
        count: { $sum: 1 },
      },
    },
    {
      $group: {
        _id: null,
        totalActivities: { $sum: '$count' },
        byType: {
          $push: {
            type: '$_id.type',
            level: '$_id.level',
            status: '$_id.status',
            count: '$count',
          },
        },
        byLevel: {
          $addToSet: {
            level: '$_id.level',
            count: '$count',
          },
        },
        byStatus: {
          $addToSet: {
            status: '$_id.status',
            count: '$count',
          },
        },
      },
    },
  ];

  const result = await this.aggregate(pipeline);
  return (
    result[0] || {
      totalActivities: 0,
      byType: [],
      byLevel: [],
      byStatus: [],
    }
  );
};

activityLogSchema.statics.cleanupOldLogs = async function (
  daysToKeep: number = 365
): Promise<number> {
  const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);

  const result = await this.deleteMany({
    timestamp: { $lt: cutoffDate },
    level: { $nin: ['critical', 'error'] }, // Keep critical and error logs longer
  });

  return result.deletedCount || 0;
};

// Create and export the model
export const ActivityLog =
  (mongoose.models.ActivityLog as IActivityLogModel) ||
  mongoose.model<IActivityLog, IActivityLogModel>('ActivityLog', activityLogSchema);

// Helper functions for Next.js API routes
export class ActivityLogService {
  static async log(logData: CreateActivityLogInput): Promise<IActivityLog> {
    try {
      return await ActivityLog.createLog(logData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async logUserActivity(
    userId: string,
    type: ActivityType,
    action: string,
    options: {
      organizationId?: string;
      description?: string;
      metadata?: Record<string, unknown>;
      ipAddress?: string;
      userAgent?: string;
      level?: ActivityLevel;
      status?: ActivityStatus;
    } = {}
  ): Promise<IActivityLog> {
    return await this.log({
      type,
      action,
      userId,
      level: options.level || 'info',
      status: options.status || 'completed',
      organizationId: options.organizationId,
      description: options.description,
      metadata: options.metadata,
      ipAddress: options.ipAddress,
      userAgent: options.userAgent,
    });
  }

  static async logSystemActivity(
    type: ActivityType,
    action: string,
    options: {
      description?: string;
      metadata?: Record<string, unknown>;
      level?: ActivityLevel;
      status?: ActivityStatus;
    } = {}
  ): Promise<IActivityLog> {
    return await this.log({
      type,
      action,
      level: options.level || 'info',
      status: options.status || 'completed',
      description: options.description,
      metadata: options.metadata,
    });
  }

  static async getUserActivityHistory(userId: string, limit?: number): Promise<IActivityLog[]> {
    return await ActivityLog.findByUser(userId, limit);
  }

  static async getOrganizationActivityHistory(
    organizationId: string,
    limit?: number
  ): Promise<IActivityLog[]> {
    return await ActivityLog.findByOrganization(organizationId, limit);
  }

  static async getActivityByType(type: ActivityType, limit?: number): Promise<IActivityLog[]> {
    return await ActivityLog.findByType(type, limit);
  }

  static async getSecurityLogs(limit?: number): Promise<IActivityLog[]> {
    return await ActivityLog.findSecurityLogs(limit);
  }

  static async getCriticalLogs(limit?: number): Promise<IActivityLog[]> {
    return await ActivityLog.findCriticalLogs(limit);
  }

  static async getRecentActivity(hours?: number, limit?: number): Promise<IActivityLog[]> {
    return await ActivityLog.findRecentLogs(hours, limit);
  }

  static async getActivityStats(startDate: Date, endDate: Date): Promise<any> {
    return await ActivityLog.getActivityStats(startDate, endDate);
  }

  static async cleanupOldLogs(daysToKeep?: number): Promise<number> {
    return await ActivityLog.cleanupOldLogs(daysToKeep);
  }

  // Convenience methods for common activities
  static async logLogin(
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<IActivityLog> {
    return await this.logUserActivity(userId, 'user_login', 'User logged in', {
      ipAddress,
      userAgent,
      level: 'info',
    });
  }

  static async logLogout(userId: string): Promise<IActivityLog> {
    return await this.logUserActivity(userId, 'user_logout', 'User logged out');
  }

  static async logPasswordChange(userId: string, ipAddress?: string): Promise<IActivityLog> {
    return await this.logUserActivity(userId, 'user_password_change', 'User changed password', {
      ipAddress,
      level: 'warning',
    });
  }

  static async logSecurityAlert(
    description: string,
    metadata?: Record<string, unknown>,
    userId?: string
  ): Promise<IActivityLog> {
    return await this.log({
      type: 'system_security_alert',
      action: 'Security alert triggered',
      description,
      metadata,
      userId,
      level: 'critical',
      status: 'pending',
    });
  }

  static async logError(
    action: string,
    error: Error,
    userId?: string,
    organizationId?: string
  ): Promise<IActivityLog> {
    return await this.log({
      type: 'system_error',
      action,
      description: error.message,
      metadata: {
        stack: error.stack,
        name: error.name,
      },
      userId,
      organizationId,
      level: 'error',
      status: 'failed',
    });
  }
}
