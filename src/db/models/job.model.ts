import mongoose, { Document, Model, Schema } from 'mongoose';
import { nanoid } from 'nanoid';
import { z } from 'zod';

// Zod validation schemas
export const JobTypeSchema = z.enum([
  'full_time',
  'part_time',
  'contract',
  'freelance',
  'internship',
  'temporary',
]);
export const JobStatusSchema = z.enum(['draft', 'published', 'paused', 'closed', 'archived']);
export const JobExperienceLevelSchema = z.enum([
  'entry',
  'junior',
  'mid',
  'senior',
  'lead',
  'executive',
]);
export const WorkLocationSchema = z.enum(['remote', 'onsite', 'hybrid']);
export const PrioritySchema = z.enum(['low', 'medium', 'high', 'urgent']);

export const SalaryRangeSchema = z.object({
  min: z.number().min(0),
  max: z.number().min(0),
  currency: z.string().length(3).default('USD'),
  period: z.enum(['hourly', 'daily', 'weekly', 'monthly', 'yearly']).default('yearly'),
  isNegotiable: z.boolean().default(false),
});

export const LocationSchema = z.object({
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().min(1, 'Country is required'),
  timezone: z.string().optional(),
  isRemoteAllowed: z.boolean().default(false),
});

export const RequirementsSchema = z.object({
  education: z
    .object({
      level: z.enum(['high_school', 'associate', 'bachelor', 'master', 'phd', 'other']).optional(),
      field: z.string().optional(),
      isRequired: z.boolean().default(false),
    })
    .optional(),
  experience: z
    .object({
      minimumYears: z.number().min(0).default(0),
      preferredYears: z.number().min(0).optional(),
      industries: z.array(z.string()).default([]),
      roles: z.array(z.string()).default([]),
    })
    .optional(),
  skills: z
    .object({
      required: z.array(z.string()).default([]),
      preferred: z.array(z.string()).default([]),
      technologies: z.array(z.string()).default([]),
    })
    .default({ required: [], preferred: [], technologies: [] }),
  languages: z
    .array(
      z.object({
        language: z.string(),
        proficiency: z.enum(['basic', 'conversational', 'fluent', 'native']),
        isRequired: z.boolean().default(false),
      })
    )
    .default([]),
  certifications: z.array(z.string()).default([]),
  other: z.array(z.string()).default([]),
});

export const BenefitsSchema = z.object({
  healthInsurance: z.boolean().default(false),
  dentalInsurance: z.boolean().default(false),
  visionInsurance: z.boolean().default(false),
  retirement401k: z.boolean().default(false),
  paidTimeOff: z.boolean().default(false),
  flexibleSchedule: z.boolean().default(false),
  remoteWork: z.boolean().default(false),
  professionalDevelopment: z.boolean().default(false),
  stockOptions: z.boolean().default(false),
  bonuses: z.boolean().default(false),
  gymMembership: z.boolean().default(false),
  other: z.array(z.string()).default([]),
});

export const InterviewProcessSchema = z.object({
  stages: z
    .array(
      z.object({
        name: z.string(),
        description: z.string().optional(),
        type: z.enum(['phone', 'video', 'onsite', 'technical', 'behavioral', 'ai_interview']),
        duration: z.number().min(15).max(480), // minutes
        isRequired: z.boolean().default(true),
        order: z.number().min(1),
      })
    )
    .default([]),
  estimatedDuration: z.string().optional(), // e.g., "2-3 weeks"
  aiInterviewEnabled: z.boolean().default(false),
  aiInterviewQuestions: z.array(z.string()).default([]),
});

export const CreateJobSchema = z.object({
  organizationId: z.string().min(1, 'Organization ID is required'),
  title: z.string().min(1, 'Job title is required'),
  description: z.string().min(50, 'Job description must be at least 50 characters'),
  department: z.string().optional(),
  type: JobTypeSchema,
  experienceLevel: JobExperienceLevelSchema,
  workLocation: WorkLocationSchema,
  location: LocationSchema,
  salaryRange: SalaryRangeSchema.optional(),
  requirements: RequirementsSchema.optional(),
  benefits: BenefitsSchema.optional(),
  interviewProcess: InterviewProcessSchema.optional(),
  applicationDeadline: z.date().optional(),
  startDate: z.date().optional(),
  priority: PrioritySchema.default('medium'),
  tags: z.array(z.string()).default([]),
  isConfidential: z.boolean().default(false),
  postedBy: z.string().min(1, 'Posted by user ID is required'),
});

export const UpdateJobSchema = CreateJobSchema.partial().omit({
  organizationId: true,
  postedBy: true,
});

// TypeScript interfaces
export type JobType = z.infer<typeof JobTypeSchema>;
export type JobStatus = z.infer<typeof JobStatusSchema>;
export type JobExperienceLevel = z.infer<typeof JobExperienceLevelSchema>;
export type WorkLocation = z.infer<typeof WorkLocationSchema>;
export type Priority = z.infer<typeof PrioritySchema>;
export type SalaryRange = z.infer<typeof SalaryRangeSchema>;
export type Location = z.infer<typeof LocationSchema>;
export type Requirements = z.infer<typeof RequirementsSchema>;
export type Benefits = z.infer<typeof BenefitsSchema>;
export type InterviewProcess = z.infer<typeof InterviewProcessSchema>;
export type CreateJobInput = z.infer<typeof CreateJobSchema>;
export type UpdateJobInput = z.infer<typeof UpdateJobSchema>;

export interface IJob extends Document {
  _id: mongoose.Types.ObjectId;
  jobId: string;
  organizationId: string;
  title: string;
  slug: string;
  description: string;
  department?: string;
  type: JobType;
  status: JobStatus;
  experienceLevel: JobExperienceLevel;
  workLocation: WorkLocation;
  location: Location;
  salaryRange?: SalaryRange;
  requirements?: Requirements;
  benefits?: Benefits;
  interviewProcess?: InterviewProcess;
  applicationDeadline?: Date;
  startDate?: Date;
  priority: Priority;
  tags: string[];
  isConfidential: boolean;
  postedBy: string;
  publishedAt?: Date;
  closedAt?: Date;
  applicationCount: number;
  viewCount: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  generateSlug(): string;
  publish(): Promise<void>;
  unpublish(): Promise<void>;
  close(): Promise<void>;
  archive(): Promise<void>;
  incrementViewCount(): Promise<void>;
  updateApplicationCount(): Promise<number>;
  isExpired(): boolean;
  canApply(): boolean;
  getPublicData(): Partial<IJob>;
  getSummary(): Partial<IJob>;
}

// Static methods interface
export interface IJobModel extends Model<IJob> {
  createJob(jobData: CreateJobInput): Promise<IJob>;
  findByJobId(jobId: string): Promise<IJob | null>;
  findBySlug(slug: string): Promise<IJob | null>;
  findByOrganization(organizationId: string): Promise<IJob[]>;
  findPublishedJobs(filters?: any): Promise<IJob[]>;
  searchJobs(query: string, filters?: any): Promise<IJob[]>;
  findBySkills(skills: string[]): Promise<IJob[]>;
  findByLocation(location: Partial<Location>): Promise<IJob[]>;
  findExpiredJobs(): Promise<IJob[]>;
  getJobStats(organizationId?: string): Promise<any>;
}

// Mongoose schema
const jobSchema = new Schema<IJob>(
  {
    jobId: {
      type: String,
      unique: true,
      default: () => `job_${nanoid(12)}`,
      index: true,
    },
    organizationId: {
      type: String,
      required: true,
      index: true,
    },
    title: {
      type: String,
      required: true,
      trim: true,
      index: true,
    },
    slug: {
      type: String,
      unique: true,
      index: true,
    },
    description: {
      type: String,
      required: true,
    },
    department: {
      type: String,
      trim: true,
      index: true,
    },
    type: {
      type: String,
      enum: ['full_time', 'part_time', 'contract', 'freelance', 'internship', 'temporary'],
      required: true,
      index: true,
    },
    status: {
      type: String,
      enum: ['draft', 'published', 'paused', 'closed', 'archived'],
      default: 'draft',
      index: true,
    },
    experienceLevel: {
      type: String,
      enum: ['entry', 'junior', 'mid', 'senior', 'lead', 'executive'],
      required: true,
      index: true,
    },
    workLocation: {
      type: String,
      enum: ['remote', 'onsite', 'hybrid'],
      required: true,
      index: true,
    },
    location: {
      city: String,
      state: String,
      country: { type: String, required: true },
      timezone: String,
      isRemoteAllowed: { type: Boolean, default: false },
    },
    salaryRange: {
      min: Number,
      max: Number,
      currency: { type: String, default: 'USD' },
      period: {
        type: String,
        enum: ['hourly', 'daily', 'weekly', 'monthly', 'yearly'],
        default: 'yearly',
      },
      isNegotiable: { type: Boolean, default: false },
    },
    requirements: {
      education: {
        level: {
          type: String,
          enum: ['high_school', 'associate', 'bachelor', 'master', 'phd', 'other'],
        },
        field: String,
        isRequired: { type: Boolean, default: false },
      },
      experience: {
        minimumYears: { type: Number, default: 0 },
        preferredYears: Number,
        industries: [String],
        roles: [String],
      },
      skills: {
        required: [String],
        preferred: [String],
        technologies: [String],
      },
      languages: [
        {
          language: String,
          proficiency: {
            type: String,
            enum: ['basic', 'conversational', 'fluent', 'native'],
          },
          isRequired: { type: Boolean, default: false },
        },
      ],
      certifications: [String],
      other: [String],
    },
    benefits: {
      healthInsurance: { type: Boolean, default: false },
      dentalInsurance: { type: Boolean, default: false },
      visionInsurance: { type: Boolean, default: false },
      retirement401k: { type: Boolean, default: false },
      paidTimeOff: { type: Boolean, default: false },
      flexibleSchedule: { type: Boolean, default: false },
      remoteWork: { type: Boolean, default: false },
      professionalDevelopment: { type: Boolean, default: false },
      stockOptions: { type: Boolean, default: false },
      bonuses: { type: Boolean, default: false },
      gymMembership: { type: Boolean, default: false },
      other: [String],
    },
    interviewProcess: {
      stages: [
        {
          name: String,
          description: String,
          type: {
            type: String,
            enum: ['phone', 'video', 'onsite', 'technical', 'behavioral', 'ai_interview'],
          },
          duration: Number,
          isRequired: { type: Boolean, default: true },
          order: Number,
        },
      ],
      estimatedDuration: String,
      aiInterviewEnabled: { type: Boolean, default: false },
      aiInterviewQuestions: [String],
    },
    applicationDeadline: {
      type: Date,
      index: true,
    },
    startDate: {
      type: Date,
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium',
      index: true,
    },
    tags: [String],
    isConfidential: {
      type: Boolean,
      default: false,
      index: true,
    },
    postedBy: {
      type: String,
      required: true,
      index: true,
    },
    publishedAt: {
      type: Date,
      index: true,
    },
    closedAt: {
      type: Date,
      index: true,
    },
    applicationCount: {
      type: Number,
      default: 0,
    },
    viewCount: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
jobSchema.index({ title: 'text', description: 'text', tags: 'text' });
jobSchema.index({ organizationId: 1, status: 1 });
jobSchema.index({ status: 1, publishedAt: -1 });
jobSchema.index({ type: 1, experienceLevel: 1 });
jobSchema.index({ workLocation: 1 });
jobSchema.index({ 'location.country': 1, 'location.city': 1 });
jobSchema.index({ 'requirements.skills.required': 1 });
jobSchema.index({ 'requirements.skills.technologies': 1 });
jobSchema.index({ applicationDeadline: 1 });
jobSchema.index({ priority: 1, publishedAt: -1 });
jobSchema.index({ isActive: 1, status: 1 });
jobSchema.index({ createdAt: -1 });

// Pre-save middleware to generate slug
jobSchema.pre('save', function (next) {
  if (this.isModified('title') || this.isNew) {
    this.slug = this.generateSlug();
  }
  next();
});

// Instance methods
jobSchema.methods.generateSlug = function (): string {
  const baseSlug = this.title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

  // Add random suffix to ensure uniqueness
  return `${baseSlug}-${nanoid(6)}`;
};

jobSchema.methods.publish = async function (): Promise<void> {
  this.status = 'published';
  this.publishedAt = new Date();
  this.isActive = true;
  await this.save();
};

jobSchema.methods.unpublish = async function (): Promise<void> {
  this.status = 'paused';
  await this.save();
};

jobSchema.methods.close = async function (): Promise<void> {
  this.status = 'closed';
  this.closedAt = new Date();
  this.isActive = false;
  await this.save();
};

jobSchema.methods.archive = async function (): Promise<void> {
  this.status = 'archived';
  this.isActive = false;
  await this.save();
};

jobSchema.methods.incrementViewCount = async function (): Promise<void> {
  this.viewCount += 1;
  await this.save();
};

jobSchema.methods.updateApplicationCount = async function (): Promise<number> {
  // This will be implemented when we create the JobApplication model
  // For now, return the current count
  return this.applicationCount;
};

jobSchema.methods.isExpired = function (): boolean {
  return !!(this.applicationDeadline && this.applicationDeadline < new Date());
};

jobSchema.methods.canApply = function (): boolean {
  return this.status === 'published' && this.isActive && !this.isExpired();
};

jobSchema.methods.getPublicData = function (): Partial<IJob> {
  const publicData: Partial<IJob> = {
    jobId: this.jobId,
    title: this.title,
    slug: this.slug,
    description: this.description,
    department: this.department,
    type: this.type,
    experienceLevel: this.experienceLevel,
    workLocation: this.workLocation,
    location: this.location,
    requirements: this.requirements,
    benefits: this.benefits,
    applicationDeadline: this.applicationDeadline,
    startDate: this.startDate,
    tags: this.tags,
    publishedAt: this.publishedAt,
    viewCount: this.viewCount,
    applicationCount: this.applicationCount,
  };

  // Include salary range if not confidential
  if (!this.isConfidential && this.salaryRange) {
    publicData.salaryRange = this.salaryRange;
  }

  return publicData;
};

jobSchema.methods.getSummary = function (): Partial<IJob> {
  return {
    jobId: this.jobId,
    title: this.title,
    slug: this.slug,
    department: this.department,
    type: this.type,
    experienceLevel: this.experienceLevel,
    workLocation: this.workLocation,
    location: this.location,
    publishedAt: this.publishedAt,
    applicationDeadline: this.applicationDeadline,
    status: this.status,
    priority: this.priority,
    applicationCount: this.applicationCount,
    viewCount: this.viewCount,
  };
};

// Static methods
jobSchema.statics.createJob = async function (jobData: CreateJobInput): Promise<IJob> {
  // Validate input with Zod
  const validatedData = CreateJobSchema.parse(jobData);

  // Create new job
  const job = new this(validatedData);
  await job.save();

  return job;
};

jobSchema.statics.findByJobId = async function (jobId: string): Promise<IJob | null> {
  return this.findOne({ jobId });
};

jobSchema.statics.findBySlug = async function (slug: string): Promise<IJob | null> {
  return this.findOne({ slug, status: 'published', isActive: true });
};

jobSchema.statics.findByOrganization = async function (organizationId: string): Promise<IJob[]> {
  return this.find({ organizationId }).sort({ createdAt: -1 });
};

jobSchema.statics.findPublishedJobs = async function (filters: any = {}): Promise<IJob[]> {
  const query = {
    status: 'published',
    isActive: true,
    ...filters,
  };

  return this.find(query).sort({ priority: -1, publishedAt: -1 }).limit(50);
};

jobSchema.statics.searchJobs = async function (query: string, filters: any = {}): Promise<IJob[]> {
  const searchQuery: any = {
    status: 'published',
    isActive: true,
    ...filters,
  };

  if (query.trim()) {
    searchQuery.$text = { $search: query };
  }

  return this.find(searchQuery)
    .sort(query.trim() ? { score: { $meta: 'textScore' } } : { publishedAt: -1 })
    .limit(50);
};

jobSchema.statics.findBySkills = async function (skills: string[]): Promise<IJob[]> {
  return this.find({
    status: 'published',
    isActive: true,
    $or: [
      { 'requirements.skills.required': { $in: skills.map((skill) => new RegExp(skill, 'i')) } },
      { 'requirements.skills.preferred': { $in: skills.map((skill) => new RegExp(skill, 'i')) } },
      {
        'requirements.skills.technologies': { $in: skills.map((skill) => new RegExp(skill, 'i')) },
      },
    ],
  }).sort({ publishedAt: -1 });
};

jobSchema.statics.findByLocation = async function (location: Partial<Location>): Promise<IJob[]> {
  const locationQuery: any = {
    status: 'published',
    isActive: true,
  };

  if (location.country) {
    locationQuery['location.country'] = new RegExp(location.country, 'i');
  }
  if (location.city) {
    locationQuery['location.city'] = new RegExp(location.city, 'i');
  }
  if (location.state) {
    locationQuery['location.state'] = new RegExp(location.state, 'i');
  }

  return this.find(locationQuery).sort({ publishedAt: -1 });
};

jobSchema.statics.findExpiredJobs = async function (): Promise<IJob[]> {
  return this.find({
    status: 'published',
    applicationDeadline: { $lt: new Date() },
  });
};

jobSchema.statics.getJobStats = async function (organizationId?: string): Promise<any> {
  const matchStage: any = {};
  if (organizationId) {
    matchStage.organizationId = organizationId;
  }

  const pipeline = [
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalJobs: { $sum: 1 },
        publishedJobs: {
          $sum: { $cond: [{ $eq: ['$status', 'published'] }, 1, 0] },
        },
        draftJobs: {
          $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] },
        },
        closedJobs: {
          $sum: { $cond: [{ $eq: ['$status', 'closed'] }, 1, 0] },
        },
        totalApplications: { $sum: '$applicationCount' },
        totalViews: { $sum: '$viewCount' },
        avgApplicationsPerJob: { $avg: '$applicationCount' },
        avgViewsPerJob: { $avg: '$viewCount' },
      },
    },
  ];

  const result = await this.aggregate(pipeline);
  return (
    result[0] || {
      totalJobs: 0,
      publishedJobs: 0,
      draftJobs: 0,
      closedJobs: 0,
      totalApplications: 0,
      totalViews: 0,
      avgApplicationsPerJob: 0,
      avgViewsPerJob: 0,
    }
  );
};

// Create and export the model
export const Job =
  (mongoose.models.Job as IJobModel) || mongoose.model<IJob, IJobModel>('Job', jobSchema);

// Helper functions for Next.js API routes
export class JobService {
  static async createJob(jobData: CreateJobInput): Promise<IJob> {
    try {
      return await Job.createJob(jobData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async getJobById(jobId: string): Promise<IJob | null> {
    return await Job.findByJobId(jobId);
  }

  static async getJobBySlug(slug: string): Promise<IJob | null> {
    return await Job.findBySlug(slug);
  }

  static async updateJob(jobId: string, updateData: UpdateJobInput): Promise<IJob | null> {
    const validatedData = UpdateJobSchema.parse(updateData);
    return await Job.findOneAndUpdate(
      { jobId },
      { $set: validatedData },
      { new: true, runValidators: true }
    );
  }

  static async getOrganizationJobs(organizationId: string): Promise<IJob[]> {
    return await Job.findByOrganization(organizationId);
  }

  static async getPublishedJobs(filters?: any): Promise<IJob[]> {
    return await Job.findPublishedJobs(filters);
  }

  static async searchJobs(query: string, filters?: any): Promise<IJob[]> {
    return await Job.searchJobs(query, filters);
  }

  static async getJobsBySkills(skills: string[]): Promise<IJob[]> {
    return await Job.findBySkills(skills);
  }

  static async getJobsByLocation(location: Partial<Location>): Promise<IJob[]> {
    return await Job.findByLocation(location);
  }

  static async publishJob(jobId: string): Promise<IJob | null> {
    const job = await Job.findByJobId(jobId);
    if (job) {
      await job.publish();
      return job;
    }
    return null;
  }

  static async unpublishJob(jobId: string): Promise<IJob | null> {
    const job = await Job.findByJobId(jobId);
    if (job) {
      await job.unpublish();
      return job;
    }
    return null;
  }

  static async closeJob(jobId: string): Promise<IJob | null> {
    const job = await Job.findByJobId(jobId);
    if (job) {
      await job.close();
      return job;
    }
    return null;
  }

  static async archiveJob(jobId: string): Promise<IJob | null> {
    const job = await Job.findByJobId(jobId);
    if (job) {
      await job.archive();
      return job;
    }
    return null;
  }

  static async incrementJobViews(jobId: string): Promise<void> {
    const job = await Job.findByJobId(jobId);
    if (job) {
      await job.incrementViewCount();
    }
  }

  static async getJobStats(organizationId?: string): Promise<any> {
    return await Job.getJobStats(organizationId);
  }

  static async getExpiredJobs(): Promise<IJob[]> {
    return await Job.findExpiredJobs();
  }

  static async getPublicJobData(jobId: string): Promise<Partial<IJob> | null> {
    const job = await Job.findByJobId(jobId);
    return job ? job.getPublicData() : null;
  }

  static async getJobSummary(jobId: string): Promise<Partial<IJob> | null> {
    const job = await Job.findByJobId(jobId);
    return job ? job.getSummary() : null;
  }

  static async deleteJob(jobId: string): Promise<boolean> {
    const result = await Job.deleteOne({ jobId });
    return result.deletedCount === 1;
  }

  // Advanced filtering methods
  static async getJobsWithFilters(filters: {
    type?: JobType[];
    experienceLevel?: JobExperienceLevel[];
    workLocation?: WorkLocation[];
    salaryMin?: number;
    salaryMax?: number;
    location?: Partial<Location>;
    skills?: string[];
    tags?: string[];
    organizationId?: string;
    limit?: number;
    offset?: number;
  }): Promise<IJob[]> {
    const query: any = {
      status: 'published',
      isActive: true,
    };

    if (filters.type?.length) {
      query.type = { $in: filters.type };
    }

    if (filters.experienceLevel?.length) {
      query.experienceLevel = { $in: filters.experienceLevel };
    }

    if (filters.workLocation?.length) {
      query.workLocation = { $in: filters.workLocation };
    }

    if (filters.salaryMin || filters.salaryMax) {
      query['salaryRange.min'] = {};
      if (filters.salaryMin) {
        query['salaryRange.min'].$gte = filters.salaryMin;
      }
      if (filters.salaryMax) {
        query['salaryRange.max'] = { $lte: filters.salaryMax };
      }
    }

    if (filters.location) {
      if (filters.location.country) {
        query['location.country'] = new RegExp(filters.location.country, 'i');
      }
      if (filters.location.city) {
        query['location.city'] = new RegExp(filters.location.city, 'i');
      }
    }

    if (filters.skills?.length) {
      query.$or = [
        {
          'requirements.skills.required': {
            $in: filters.skills.map((skill) => new RegExp(skill, 'i')),
          },
        },
        {
          'requirements.skills.preferred': {
            $in: filters.skills.map((skill) => new RegExp(skill, 'i')),
          },
        },
        {
          'requirements.skills.technologies': {
            $in: filters.skills.map((skill) => new RegExp(skill, 'i')),
          },
        },
      ];
    }

    if (filters.tags?.length) {
      query.tags = { $in: filters.tags };
    }

    if (filters.organizationId) {
      query.organizationId = filters.organizationId;
    }

    return await Job.find(query)
      .sort({ priority: -1, publishedAt: -1 })
      .limit(filters.limit || 50)
      .skip(filters.offset || 0);
  }
}
