import mongoose, { Document, Model, Schema } from 'mongoose';
import { nanoid } from 'nanoid';
import { z } from 'zod';

// Zod validation schemas
export const OtpTypeSchema = z.enum([
  'email_verification',
  'password_reset',
  'login_verification',
  'phone_verification',
]);
export const OtpStatusSchema = z.enum(['pending', 'verified', 'expired', 'failed']);

export const CreateOtpSchema = z.object({
  email: z.string().email('Invalid email format'),
  type: OtpTypeSchema,
  code: z.string().length(6, 'OTP code must be 6 digits'),
  userId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export const VerifyOtpSchema = z.object({
  email: z.string().email('Invalid email format'),
  code: z.string().length(6, 'OTP code must be 6 digits'),
  type: OtpTypeSchema,
});

// TypeScript interfaces
export type OtpType = z.infer<typeof OtpTypeSchema>;
export type OtpStatus = z.infer<typeof OtpStatusSchema>;
export type CreateOtpInput = z.infer<typeof CreateOtpSchema>;
export type VerifyOtpInput = z.infer<typeof VerifyOtpSchema>;

export interface IOtp extends Document {
  _id: mongoose.Types.ObjectId;
  otpId: string;
  email: string;
  code: string;
  type: OtpType;
  status: OtpStatus;
  userId?: string;
  attempts: number;
  maxAttempts: number;
  expiresAt: Date;
  verifiedAt?: Date;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  isExpired(): boolean;
  isMaxAttemptsReached(): boolean;
  canVerify(): boolean;
  markAsVerified(): Promise<void>;
  incrementAttempts(): Promise<void>;
  generateNewCode(): string;
}

// Static methods interface
export interface IOtpModel extends Model<IOtp> {
  createOtp(otpData: CreateOtpInput): Promise<IOtp>;
  verifyOtp(
    verifyData: VerifyOtpInput
  ): Promise<{ otp: IOtp | null; isValid: boolean; message: string }>;
  findValidOtp(email: string, type: OtpType): Promise<IOtp | null>;
  cleanupExpiredOtps(): Promise<number>;
  findByEmail(email: string): Promise<IOtp[]>;
  findByUserId(userId: string): Promise<IOtp[]>;
}

// Mongoose schema
const otpSchema = new Schema<IOtp>(
  {
    otpId: {
      type: String,
      unique: true,
      default: () => `otp_${nanoid(12)}`,
      index: true,
    },
    email: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
      index: true,
    },
    code: {
      type: String,
      required: true,
      length: 6,
    },
    type: {
      type: String,
      enum: ['email_verification', 'password_reset', 'login_verification', 'phone_verification'],
      required: true,
      index: true,
    },
    status: {
      type: String,
      enum: ['pending', 'verified', 'expired', 'failed'],
      default: 'pending',
      index: true,
    },
    userId: {
      type: String,
      index: true,
    },
    attempts: {
      type: Number,
      default: 0,
    },
    maxAttempts: {
      type: Number,
      default: 3,
    },
    expiresAt: {
      type: Date,
      required: true,
      index: true,
    },
    verifiedAt: {
      type: Date,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
otpSchema.index({ email: 1, type: 1 });
otpSchema.index({ email: 1, code: 1, type: 1 });
otpSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index
otpSchema.index({ status: 1 });
otpSchema.index({ createdAt: -1 });

// Instance methods
otpSchema.methods.isExpired = function (): boolean {
  return new Date() > this.expiresAt;
};

otpSchema.methods.isMaxAttemptsReached = function (): boolean {
  return this.attempts >= this.maxAttempts;
};

otpSchema.methods.canVerify = function (): boolean {
  return this.status === 'pending' && !this.isExpired() && !this.isMaxAttemptsReached();
};

otpSchema.methods.markAsVerified = async function (): Promise<void> {
  this.status = 'verified';
  this.verifiedAt = new Date();
  await this.save();
};

otpSchema.methods.incrementAttempts = async function (): Promise<void> {
  this.attempts += 1;

  if (this.isMaxAttemptsReached()) {
    this.status = 'failed';
  }

  await this.save();
};

otpSchema.methods.generateNewCode = function (): string {
  // Generate 6-digit numeric code
  const code = Math.floor(100000 + Math.random() * 900000).toString();
  this.code = code;
  this.attempts = 0;
  this.status = 'pending';
  this.expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  return code;
};

// Static methods
otpSchema.statics.createOtp = async function (otpData: CreateOtpInput): Promise<IOtp> {
  // Validate input with Zod
  const validatedData = CreateOtpSchema.parse(otpData);

  // Invalidate any existing pending OTPs for the same email and type
  await this.updateMany(
    {
      email: validatedData.email,
      type: validatedData.type,
      status: 'pending',
    },
    {
      $set: { status: 'expired' },
    }
  );

  // Create new OTP with expiration (10 minutes from now)
  const otpDoc = new this({
    ...validatedData,
    expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
  });

  await otpDoc.save();
  return otpDoc;
};

otpSchema.statics.verifyOtp = async function (
  verifyData: VerifyOtpInput
): Promise<{ otp: IOtp | null; isValid: boolean; message: string }> {
  const { email, code, type } = VerifyOtpSchema.parse(verifyData);

  const otp = await this.findOne({
    email: email.toLowerCase(),
    code,
    type,
    status: 'pending',
  });

  if (!otp) {
    return {
      otp: null,
      isValid: false,
      message: 'Invalid or expired OTP code',
    };
  }

  // Check if OTP can be verified
  if (!otp.canVerify()) {
    if (otp.isExpired()) {
      otp.status = 'expired';
      await otp.save();
      return {
        otp,
        isValid: false,
        message: 'OTP code has expired',
      };
    }

    if (otp.isMaxAttemptsReached()) {
      return {
        otp,
        isValid: false,
        message: 'Maximum verification attempts reached',
      };
    }
  }

  // Mark as verified
  await otp.markAsVerified();

  return {
    otp,
    isValid: true,
    message: 'OTP verified successfully',
  };
};

otpSchema.statics.findValidOtp = async function (
  email: string,
  type: OtpType
): Promise<IOtp | null> {
  return this.findOne({
    email: email.toLowerCase(),
    type,
    status: 'pending',
    expiresAt: { $gt: new Date() },
  });
};

otpSchema.statics.cleanupExpiredOtps = async function (): Promise<number> {
  const result = await this.deleteMany({
    $or: [
      { expiresAt: { $lt: new Date() } },
      { status: { $in: ['expired', 'verified', 'failed'] } },
    ],
  });

  return result.deletedCount || 0;
};

otpSchema.statics.findByEmail = async function (email: string): Promise<IOtp[]> {
  return this.find({ email: email.toLowerCase() }).sort({ createdAt: -1 });
};

otpSchema.statics.findByUserId = async function (userId: string): Promise<IOtp[]> {
  return this.find({ userId }).sort({ createdAt: -1 });
};

// Create and export the model
export const Otp =
  (mongoose.models.Otp as IOtpModel) || mongoose.model<IOtp, IOtpModel>('Otp', otpSchema);

// Helper functions for Next.js API routes
export class OtpService {
  static async generateOtp(otpData: CreateOtpInput): Promise<IOtp> {
    try {
      return await Otp.createOtp(otpData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async verifyOtp(
    verifyData: VerifyOtpInput
  ): Promise<{ otp: IOtp | null; isValid: boolean; message: string }> {
    try {
      return await Otp.verifyOtp(verifyData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async generateEmailVerificationOtp(email: string, userId?: string): Promise<IOtp> {
    const code = Math.floor(100000 + Math.random() * 900000).toString();

    return await this.generateOtp({
      email,
      code,
      type: 'email_verification',
      userId,
    });
  }

  static async generatePasswordResetOtp(email: string, userId?: string): Promise<IOtp> {
    const code = Math.floor(100000 + Math.random() * 900000).toString();

    return await this.generateOtp({
      email,
      code,
      type: 'password_reset',
      userId,
    });
  }

  static async generateLoginVerificationOtp(email: string, userId?: string): Promise<IOtp> {
    const code = Math.floor(100000 + Math.random() * 900000).toString();

    return await this.generateOtp({
      email,
      code,
      type: 'login_verification',
      userId,
      metadata: {
        userAgent: 'unknown',
        ipAddress: 'unknown',
      },
    });
  }

  static async resendOtp(email: string, type: OtpType): Promise<IOtp> {
    // Find existing OTP
    const existingOtp = await Otp.findValidOtp(email, type);

    if (existingOtp) {
      // Generate new code for existing OTP
      existingOtp.generateNewCode();
      await existingOtp.save();
      return existingOtp;
    }

    // Create new OTP if none exists
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    return await this.generateOtp({
      email,
      code,
      type,
    });
  }

  static async cleanupExpired(): Promise<number> {
    return await Otp.cleanupExpiredOtps();
  }

  static async getOtpHistory(email: string): Promise<IOtp[]> {
    return await Otp.findByEmail(email);
  }
}
