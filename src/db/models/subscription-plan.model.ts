import mongoose, { Document, Model, Schema } from 'mongoose';
import { nanoid } from 'nanoid';
import { z } from 'zod';

// Zod validation schemas
export const PlanTypeSchema = z.enum(['free', 'basic', 'professional', 'enterprise', 'custom']);
export const BillingIntervalSchema = z.enum(['monthly', 'quarterly', 'yearly', 'lifetime']);
export const PlanStatusSchema = z.enum(['active', 'inactive', 'deprecated', 'coming_soon']);
export const PaymentProviderSchema = z.enum(['stripe', 'razorpay', 'polar']);

export const PricingSchema = z.object({
  amount: z.number().min(0),
  currency: z.string().length(3).default('USD'),
  interval: BillingIntervalSchema,
  intervalCount: z.number().min(1).default(1), // e.g., every 3 months
});

export const FeatureLimitSchema = z.object({
  name: z.string(),
  limit: z.number().min(-1), // -1 for unlimited
  description: z.string().optional(),
});

export const PlanFeaturesSchema = z.object({
  // Core features
  maxJobs: z.number().min(-1).default(-1), // -1 for unlimited
  maxApplicationsPerJob: z.number().min(-1).default(-1),
  maxOrganizationMembers: z.number().min(-1).default(-1),
  maxAiInterviewsPerMonth: z.number().min(-1).default(-1),
  maxCandidatesPerMonth: z.number().min(-1).default(-1),

  // AI Features
  aiInterviewEnabled: z.boolean().default(false),
  aiAnalysisEnabled: z.boolean().default(false),
  aiRecommendationsEnabled: z.boolean().default(false),
  customAiQuestions: z.boolean().default(false),

  // Advanced features
  customBranding: z.boolean().default(false),
  advancedAnalytics: z.boolean().default(false),
  apiAccess: z.boolean().default(false),
  ssoIntegration: z.boolean().default(false),
  prioritySupport: z.boolean().default(false),
  dedicatedAccountManager: z.boolean().default(false),

  // Integration features
  atsIntegration: z.boolean().default(false),
  slackIntegration: z.boolean().default(false),
  teamsIntegration: z.boolean().default(false),
  webhooksEnabled: z.boolean().default(false),

  // Storage and export
  dataRetentionMonths: z.number().min(1).default(12),
  dataExportEnabled: z.boolean().default(false),
  bulkOperations: z.boolean().default(false),

  // Custom limits
  customLimits: z.array(FeatureLimitSchema).default([]),
});

export const PaymentProviderConfigSchema = z.object({
  provider: PaymentProviderSchema,
  productId: z.string().optional(), // Stripe product ID, Razorpay plan ID, etc.
  priceId: z.string().optional(), // Stripe price ID
  planId: z.string().optional(), // Provider-specific plan ID
  webhookEndpoint: z.string().url().optional(),
  isActive: z.boolean().default(true),
});

export const CreateSubscriptionPlanSchema = z.object({
  name: z.string().min(1, 'Plan name is required'),
  description: z.string().optional(),
  type: PlanTypeSchema,
  status: PlanStatusSchema.default('active'),
  pricing: z.array(PricingSchema).min(1, 'At least one pricing option is required'),
  features: PlanFeaturesSchema,
  paymentProviders: z.array(PaymentProviderConfigSchema).default([]),
  trialDays: z.number().min(0).default(0),
  setupFee: z.number().min(0).default(0),
  isPopular: z.boolean().default(false),
  sortOrder: z.number().default(0),
  metadata: z.record(z.unknown()).optional(),
});

export const UpdateSubscriptionPlanSchema = CreateSubscriptionPlanSchema.partial();

// TypeScript interfaces
export type PlanType = z.infer<typeof PlanTypeSchema>;
export type BillingInterval = z.infer<typeof BillingIntervalSchema>;
export type PlanStatus = z.infer<typeof PlanStatusSchema>;
export type PaymentProvider = z.infer<typeof PaymentProviderSchema>;
export type Pricing = z.infer<typeof PricingSchema>;
export type FeatureLimit = z.infer<typeof FeatureLimitSchema>;
export type PlanFeatures = z.infer<typeof PlanFeaturesSchema>;
export type PaymentProviderConfig = z.infer<typeof PaymentProviderConfigSchema>;
export type CreateSubscriptionPlanInput = z.infer<typeof CreateSubscriptionPlanSchema>;
export type UpdateSubscriptionPlanInput = z.infer<typeof UpdateSubscriptionPlanSchema>;

export interface ISubscriptionPlan extends Document {
  _id: mongoose.Types.ObjectId;
  planId: string;
  name: string;
  description?: string;
  type: PlanType;
  status: PlanStatus;
  pricing: Pricing[];
  features: PlanFeatures;
  paymentProviders: PaymentProviderConfig[];
  trialDays: number;
  setupFee: number;
  isPopular: boolean;
  sortOrder: number;
  metadata?: Record<string, unknown>;
  subscriberCount: number;
  revenueGenerated: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  getPricingForInterval(interval: BillingInterval): Pricing | null;
  getPricingForProvider(provider: PaymentProvider, interval: BillingInterval): Pricing | null;
  hasFeature(featureName: keyof PlanFeatures): boolean;
  getFeatureLimit(featureName: string): number;
  canUpgradeFrom(otherPlan: ISubscriptionPlan): boolean;
  canDowngradeFrom(otherPlan: ISubscriptionPlan): boolean;
  calculateProration(fromPlan: ISubscriptionPlan, daysRemaining: number): number;
  getDisplayPrice(interval?: BillingInterval): string;
  isFeatureEnabled(featureName: keyof PlanFeatures): boolean;
  getProviderConfig(provider: PaymentProvider): PaymentProviderConfig | null;
}

// Static methods interface
export interface ISubscriptionPlanModel extends Model<ISubscriptionPlan> {
  createPlan(planData: CreateSubscriptionPlanInput): Promise<ISubscriptionPlan>;
  findByPlanId(planId: string): Promise<ISubscriptionPlan | null>;
  findActivePlans(): Promise<ISubscriptionPlan[]>;
  findByType(type: PlanType): Promise<ISubscriptionPlan[]>;
  findByProvider(provider: PaymentProvider): Promise<ISubscriptionPlan[]>;
  getPublicPlans(): Promise<ISubscriptionPlan[]>;
  getPlanComparison(): Promise<any>;
  findUpgradePath(currentPlanId: string): Promise<ISubscriptionPlan[]>;
  findDowngradePath(currentPlanId: string): Promise<ISubscriptionPlan[]>;
}

// Mongoose schema
const subscriptionPlanSchema = new Schema<ISubscriptionPlan>(
  {
    planId: {
      type: String,
      unique: true,
      default: () => `plan_${nanoid(12)}`,
      index: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
      index: true,
    },
    description: {
      type: String,
      trim: true,
    },
    type: {
      type: String,
      enum: ['free', 'basic', 'professional', 'enterprise', 'custom'],
      required: true,
      index: true,
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'deprecated', 'coming_soon'],
      default: 'active',
      index: true,
    },
    pricing: [
      {
        amount: { type: Number, required: true, min: 0 },
        currency: { type: String, required: true, length: 3, default: 'USD' },
        interval: {
          type: String,
          enum: ['monthly', 'quarterly', 'yearly', 'lifetime'],
          required: true,
        },
        intervalCount: { type: Number, default: 1, min: 1 },
      },
    ],
    features: {
      // Core features
      maxJobs: { type: Number, default: -1 },
      maxApplicationsPerJob: { type: Number, default: -1 },
      maxOrganizationMembers: { type: Number, default: -1 },
      maxAiInterviewsPerMonth: { type: Number, default: -1 },
      maxCandidatesPerMonth: { type: Number, default: -1 },

      // AI Features
      aiInterviewEnabled: { type: Boolean, default: false },
      aiAnalysisEnabled: { type: Boolean, default: false },
      aiRecommendationsEnabled: { type: Boolean, default: false },
      customAiQuestions: { type: Boolean, default: false },

      // Advanced features
      customBranding: { type: Boolean, default: false },
      advancedAnalytics: { type: Boolean, default: false },
      apiAccess: { type: Boolean, default: false },
      ssoIntegration: { type: Boolean, default: false },
      prioritySupport: { type: Boolean, default: false },
      dedicatedAccountManager: { type: Boolean, default: false },

      // Integration features
      atsIntegration: { type: Boolean, default: false },
      slackIntegration: { type: Boolean, default: false },
      teamsIntegration: { type: Boolean, default: false },
      webhooksEnabled: { type: Boolean, default: false },

      // Storage and export
      dataRetentionMonths: { type: Number, default: 12, min: 1 },
      dataExportEnabled: { type: Boolean, default: false },
      bulkOperations: { type: Boolean, default: false },

      // Custom limits
      customLimits: [
        {
          name: { type: String, required: true },
          limit: { type: Number, required: true, min: -1 },
          description: String,
        },
      ],
    },
    paymentProviders: [
      {
        provider: {
          type: String,
          enum: ['stripe', 'razorpay', 'polar'],
          required: true,
        },
        productId: String,
        priceId: String,
        planId: String,
        webhookEndpoint: String,
        isActive: { type: Boolean, default: true },
      },
    ],
    trialDays: {
      type: Number,
      default: 0,
      min: 0,
    },
    setupFee: {
      type: Number,
      default: 0,
      min: 0,
    },
    isPopular: {
      type: Boolean,
      default: false,
      index: true,
    },
    sortOrder: {
      type: Number,
      default: 0,
      index: true,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    subscriberCount: {
      type: Number,
      default: 0,
    },
    revenueGenerated: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
subscriptionPlanSchema.index({ type: 1, status: 1 });
subscriptionPlanSchema.index({ isActive: 1, sortOrder: 1 });
subscriptionPlanSchema.index({ 'paymentProviders.provider': 1 });
subscriptionPlanSchema.index({ isPopular: 1, sortOrder: 1 });
subscriptionPlanSchema.index({ createdAt: -1 });

// Instance methods
subscriptionPlanSchema.methods.getPricingForInterval = function (
  interval: BillingInterval
): Pricing | null {
  return this.pricing.find((p: any) => p.interval === interval) || null;
};

subscriptionPlanSchema.methods.getPricingForProvider = function (
  provider: PaymentProvider,
  interval: BillingInterval
): Pricing | null {
  const providerConfig = this.getProviderConfig(provider);
  if (!providerConfig || !providerConfig.isActive) return null;

  return this.getPricingForInterval(interval);
};

subscriptionPlanSchema.methods.hasFeature = function (featureName: keyof PlanFeatures): boolean {
  return this.features[featureName] === true;
};

subscriptionPlanSchema.methods.getFeatureLimit = function (featureName: string): number {
  // Check if it's a direct feature limit
  if (this.features[featureName as keyof PlanFeatures] !== undefined) {
    return this.features[featureName as keyof PlanFeatures] as number;
  }

  // Check custom limits
  const customLimit = this.features.customLimits.find((limit: any) => limit.name === featureName);
  return customLimit ? customLimit.limit : 0;
};

subscriptionPlanSchema.methods.canUpgradeFrom = function (otherPlan: ISubscriptionPlan): boolean {
  const planHierarchy = { free: 0, basic: 1, professional: 2, enterprise: 3, custom: 4 };
  return planHierarchy[this.type as PlanType] > planHierarchy[otherPlan.type];
};

subscriptionPlanSchema.methods.canDowngradeFrom = function (otherPlan: ISubscriptionPlan): boolean {
  const planHierarchy = { free: 0, basic: 1, professional: 2, enterprise: 3, custom: 4 };
  return planHierarchy[this.type as PlanType] < planHierarchy[otherPlan.type];
};

subscriptionPlanSchema.methods.calculateProration = function (
  fromPlan: ISubscriptionPlan,
  daysRemaining: number
): number {
  const currentMonthlyPrice = this.getPricingForInterval('monthly');
  const fromMonthlyPrice = fromPlan.getPricingForInterval('monthly');

  if (!currentMonthlyPrice || !fromMonthlyPrice) return 0;

  const dailyDifference = (currentMonthlyPrice.amount - fromMonthlyPrice.amount) / 30;
  return dailyDifference * daysRemaining;
};

subscriptionPlanSchema.methods.getDisplayPrice = function (interval?: BillingInterval): string {
  const pricing = interval ? this.getPricingForInterval(interval) : this.pricing[0];
  if (!pricing) return 'Contact us';

  if (pricing.amount === 0) return 'Free';

  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: pricing.currency,
  });

  return `${formatter.format(pricing.amount)}/${pricing.interval}`;
};

subscriptionPlanSchema.methods.isFeatureEnabled = function (
  featureName: keyof PlanFeatures
): boolean {
  const value = this.features[featureName];
  if (typeof value === 'boolean') return value;
  if (typeof value === 'number') return value > 0 || value === -1; // -1 means unlimited
  return false;
};

subscriptionPlanSchema.methods.getProviderConfig = function (
  provider: PaymentProvider
): PaymentProviderConfig | null {
  return (
    this.paymentProviders.find((config: any) => config.provider === provider && config.isActive) ||
    null
  );
};

// Static methods
subscriptionPlanSchema.statics.createPlan = async function (
  planData: CreateSubscriptionPlanInput
): Promise<ISubscriptionPlan> {
  // Validate input with Zod
  const validatedData = CreateSubscriptionPlanSchema.parse(planData);

  // Create new plan
  const plan = new this(validatedData);
  await plan.save();

  return plan;
};

subscriptionPlanSchema.statics.findByPlanId = async function (
  planId: string
): Promise<ISubscriptionPlan | null> {
  return this.findOne({ planId });
};

subscriptionPlanSchema.statics.findActivePlans = async function (): Promise<ISubscriptionPlan[]> {
  return this.find({ status: 'active', isActive: true }).sort({ sortOrder: 1, createdAt: 1 });
};

subscriptionPlanSchema.statics.findByType = async function (
  type: PlanType
): Promise<ISubscriptionPlan[]> {
  return this.find({ type, status: 'active', isActive: true }).sort({ sortOrder: 1 });
};

subscriptionPlanSchema.statics.findByProvider = async function (
  provider: PaymentProvider
): Promise<ISubscriptionPlan[]> {
  return this.find({
    'paymentProviders.provider': provider,
    'paymentProviders.isActive': true,
    status: 'active',
    isActive: true,
  }).sort({ sortOrder: 1 });
};

subscriptionPlanSchema.statics.getPublicPlans = async function (): Promise<ISubscriptionPlan[]> {
  return this.find({
    status: 'active',
    isActive: true,
    type: { $ne: 'custom' }, // Exclude custom plans from public listing
  }).sort({ sortOrder: 1, createdAt: 1 });
};

subscriptionPlanSchema.statics.getPlanComparison = async function (): Promise<any> {
  const plans: ISubscriptionPlan[] = await this.find({
    status: 'active',
    isActive: true,
    type: { $ne: 'custom' }, // Exclude custom plans from public listing
  }).sort({ sortOrder: 1, createdAt: 1 });

  const comparison = {
    plans: plans.map((plan) => ({
      planId: plan.planId,
      name: plan.name,
      description: plan.description,
      type: plan.type,
      pricing: plan.pricing,
      features: plan.features,
      isPopular: plan.isPopular,
      trialDays: plan.trialDays,
    })),
    featureMatrix: {} as Record<string, Record<string, any>>,
  };

  // Build feature matrix
  const allFeatures = new Set<string>();
  plans.forEach((plan) => {
    Object.keys(plan.features.toObject()).forEach((feature) => allFeatures.add(feature));
  });

  allFeatures.forEach((feature) => {
    comparison.featureMatrix[feature] = {};
    plans.forEach((plan) => {
      comparison.featureMatrix[feature][plan.planId] = plan.features[feature as keyof PlanFeatures];
    });
  });

  return comparison;
};

subscriptionPlanSchema.statics.findUpgradePath = async function (
  currentPlanId: string
): Promise<ISubscriptionPlan[]> {
  const currentPlan = await this.findByPlanId(currentPlanId);
  if (!currentPlan) return [];

  const allPlans = await this.findActivePlans();
  return allPlans.filter((plan) => plan.canUpgradeFrom(currentPlan));
};

subscriptionPlanSchema.statics.findDowngradePath = async function (
  currentPlanId: string
): Promise<ISubscriptionPlan[]> {
  const currentPlan = await this.findByPlanId(currentPlanId);
  if (!currentPlan) return [];

  const allPlans = await this.findActivePlans();
  return allPlans.filter((plan) => plan.canDowngradeFrom(currentPlan));
};

// Create and export the model
export const SubscriptionPlan =
  (mongoose.models.SubscriptionPlan as ISubscriptionPlanModel) ||
  mongoose.model<ISubscriptionPlan, ISubscriptionPlanModel>(
    'SubscriptionPlan',
    subscriptionPlanSchema
  );

// Helper functions for Next.js API routes
export class SubscriptionPlanService {
  static async createPlan(planData: CreateSubscriptionPlanInput): Promise<ISubscriptionPlan> {
    try {
      return await SubscriptionPlan.createPlan(planData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  static async getPlanById(planId: string): Promise<ISubscriptionPlan | null> {
    return await SubscriptionPlan.findByPlanId(planId);
  }

  static async updatePlan(
    planId: string,
    updateData: UpdateSubscriptionPlanInput
  ): Promise<ISubscriptionPlan | null> {
    const validatedData = UpdateSubscriptionPlanSchema.parse(updateData);
    return await SubscriptionPlan.findOneAndUpdate(
      { planId },
      { $set: validatedData },
      { new: true, runValidators: true }
    );
  }

  static async getActivePlans(): Promise<ISubscriptionPlan[]> {
    return await SubscriptionPlan.findActivePlans();
  }

  static async getPublicPlans(): Promise<ISubscriptionPlan[]> {
    return await SubscriptionPlan.getPublicPlans();
  }

  static async getPlansByType(type: PlanType): Promise<ISubscriptionPlan[]> {
    return await SubscriptionPlan.findByType(type);
  }

  static async getPlansByProvider(provider: PaymentProvider): Promise<ISubscriptionPlan[]> {
    return await SubscriptionPlan.findByProvider(provider);
  }

  static async getPlanComparison(): Promise<any> {
    return await SubscriptionPlan.getPlanComparison();
  }

  static async getUpgradePath(currentPlanId: string): Promise<ISubscriptionPlan[]> {
    return await SubscriptionPlan.findUpgradePath(currentPlanId);
  }

  static async getDowngradePath(currentPlanId: string): Promise<ISubscriptionPlan[]> {
    return await SubscriptionPlan.findDowngradePath(currentPlanId);
  }

  static async calculateUpgradeProration(
    fromPlanId: string,
    toPlanId: string,
    daysRemaining: number
  ): Promise<number> {
    const fromPlan = await SubscriptionPlan.findByPlanId(fromPlanId);
    const toPlan = await SubscriptionPlan.findByPlanId(toPlanId);

    if (!fromPlan || !toPlan) return 0;

    return toPlan.calculateProration(fromPlan, daysRemaining);
  }

  static async addPaymentProvider(
    planId: string,
    providerConfig: PaymentProviderConfig
  ): Promise<ISubscriptionPlan | null> {
    const plan = await SubscriptionPlan.findByPlanId(planId);
    if (!plan) return null;

    // Check if provider already exists
    const existingProviderIndex = plan.paymentProviders.findIndex(
      (config: any) => config.provider === providerConfig.provider
    );

    if (existingProviderIndex >= 0) {
      // Update existing provider
      plan.paymentProviders[existingProviderIndex] = providerConfig;
    } else {
      // Add new provider
      plan.paymentProviders.push(providerConfig);
    }

    await plan.save();
    return plan;
  }

  static async removePaymentProvider(
    planId: string,
    provider: PaymentProvider
  ): Promise<ISubscriptionPlan | null> {
    const plan = await SubscriptionPlan.findByPlanId(planId);
    if (!plan) return null;

    plan.paymentProviders = plan.paymentProviders.filter(
      (config: any) => config.provider !== provider
    );
    await plan.save();
    return plan;
  }

  static async updateSubscriberCount(
    planId: string,
    increment: number = 1
  ): Promise<ISubscriptionPlan | null> {
    return await SubscriptionPlan.findOneAndUpdate(
      { planId },
      { $inc: { subscriberCount: increment } },
      { new: true }
    );
  }

  static async updateRevenueGenerated(
    planId: string,
    amount: number
  ): Promise<ISubscriptionPlan | null> {
    return await SubscriptionPlan.findOneAndUpdate(
      { planId },
      { $inc: { revenueGenerated: amount } },
      { new: true }
    );
  }

  static async deactivatePlan(planId: string): Promise<ISubscriptionPlan | null> {
    return await SubscriptionPlan.findOneAndUpdate(
      { planId },
      { $set: { isActive: false, status: 'inactive' } },
      { new: true }
    );
  }

  static async activatePlan(planId: string): Promise<ISubscriptionPlan | null> {
    return await SubscriptionPlan.findOneAndUpdate(
      { planId },
      { $set: { isActive: true, status: 'active' } },
      { new: true }
    );
  }

  static async deprecatePlan(planId: string): Promise<ISubscriptionPlan | null> {
    return await SubscriptionPlan.findOneAndUpdate(
      { planId },
      { $set: { status: 'deprecated' } },
      { new: true }
    );
  }

  static async deletePlan(planId: string): Promise<boolean> {
    const result = await SubscriptionPlan.deleteOne({ planId });
    return result.deletedCount === 1;
  }

  // Provider-specific methods
  static async getStripeConfig(planId: string): Promise<PaymentProviderConfig | null> {
    const plan = await SubscriptionPlan.findByPlanId(planId);
    return plan ? plan.getProviderConfig('stripe') : null;
  }

  static async getRazorpayConfig(planId: string): Promise<PaymentProviderConfig | null> {
    const plan = await SubscriptionPlan.findByPlanId(planId);
    return plan ? plan.getProviderConfig('razorpay') : null;
  }

  static async getPolarConfig(planId: string): Promise<PaymentProviderConfig | null> {
    const plan = await SubscriptionPlan.findByPlanId(planId);
    return plan ? plan.getProviderConfig('polar') : null;
  }

  // Feature checking methods
  static async checkFeatureAccess(
    planId: string,
    featureName: keyof PlanFeatures
  ): Promise<boolean> {
    const plan = await SubscriptionPlan.findByPlanId(planId);
    return plan ? plan.isFeatureEnabled(featureName) : false;
  }

  static async getFeatureLimit(planId: string, featureName: string): Promise<number> {
    const plan = await SubscriptionPlan.findByPlanId(planId);
    return plan ? plan.getFeatureLimit(featureName) : 0;
  }

  // Analytics and reporting
  static async getPlanAnalytics(): Promise<any> {
    const pipeline = [
      {
        $group: {
          _id: '$type',
          totalPlans: { $sum: 1 },
          activePlans: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
          totalSubscribers: { $sum: '$subscriberCount' },
          totalRevenue: { $sum: '$revenueGenerated' },
          avgPrice: { $avg: { $arrayElemAt: ['$pricing.amount', 0] } },
        },
      },
      {
        $sort: { totalSubscribers: -1 },
      },
    ];

    const result = await SubscriptionPlan.aggregate(pipeline as any[]);

    const summary = await SubscriptionPlan.aggregate([
      {
        $group: {
          _id: null,
          totalPlans: { $sum: 1 },
          totalSubscribers: { $sum: '$subscriberCount' },
          totalRevenue: { $sum: '$revenueGenerated' },
          avgSubscribersPerPlan: { $avg: '$subscriberCount' },
          avgRevenuePerPlan: { $avg: '$revenueGenerated' },
        },
      },
    ]);

    return {
      byType: result,
      summary: summary[0] || {
        totalPlans: 0,
        totalSubscribers: 0,
        totalRevenue: 0,
        avgSubscribersPerPlan: 0,
        avgRevenuePerPlan: 0,
      },
    };
  }

  static async getPopularPlans(limit: number = 5): Promise<ISubscriptionPlan[]> {
    return await SubscriptionPlan.find({
      status: 'active',
      isActive: true,
    })
      .sort({ subscriberCount: -1, isPopular: -1 })
      .limit(limit);
  }

  static async getRecommendedPlan(
    organizationSize: number,
    monthlyJobPostings: number,
    needsAI: boolean
  ): Promise<ISubscriptionPlan | null> {
    const query: any = {
      status: 'active',
      isActive: true,
      type: { $ne: 'custom' },
    };

    // Basic logic for plan recommendation
    if (organizationSize <= 5 && monthlyJobPostings <= 10) {
      query.type = { $in: ['free', 'basic'] };
    } else if (organizationSize <= 50 && monthlyJobPostings <= 50) {
      query.type = { $in: ['basic', 'professional'] };
    } else {
      query.type = { $in: ['professional', 'enterprise'] };
    }

    if (needsAI) {
      query['features.aiInterviewEnabled'] = true;
    }

    const plans = await SubscriptionPlan.find(query).sort({ sortOrder: 1 });
    return plans[0] || null;
  }
}
