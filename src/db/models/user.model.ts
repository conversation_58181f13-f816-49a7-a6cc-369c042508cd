import bcrypt from 'bcryptjs';
import mongoose, { Schema } from 'mongoose';
import { nanoid } from 'nanoid';

import type { CreateUserInput, IUser, IUserModel, UserRole } from '../../@types';
import { CreateUserSchema } from '../../@types';

// Mongoose schema
const userSchema = new Schema<IUser>(
  {
    userId: {
      type: String,
      unique: true,
      default: () => `user_${nanoid(12)}`,
      index: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
      index: true,
    },
    password: {
      type: String,
      required: true,
      minlength: 8,
    },
    firstName: {
      type: String,
      required: true,
      trim: true,
    },
    lastName: {
      type: String,
      required: true,
      trim: true,
    },
    role: {
      type: String,
      enum: ['admin', 'recruiter', 'candidate', 'interviewer'],
      default: 'candidate',
      index: true,
    },
    phone: {
      type: String,
      trim: true,
    },
    avatar: {
      type: String,
    },
    timezone: {
      type: String,
      default: 'UTC',
    },
    isEmailVerified: {
      type: Boolean,
      default: false,
      index: true,
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended', 'pending'],
      default: 'pending',
      index: true,
    },
    lastLoginAt: {
      type: Date,
    },
    passwordResetToken: {
      type: String,
    },
    passwordResetExpires: {
      type: Date,
    },
    emailVerifiedAt: {
      type: Date,
    },
    lastActiveAt: {
      type: Date,
    },
    loginAttempts: {
      type: Number,
      default: 0,
    },
    lockUntil: {
      type: Date,
    },
    acceptedTerms: {
      type: Boolean,
      default: true,
    },
    acceptedTermsAt: {
      type: Date,
    },
    marketingConsent: {
      type: Boolean,
      default: false,
    },
    twoFactorEnabled: {
      type: Boolean,
      default: false,
    },
    twoFactorSecret: {
      type: String,
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (_doc, ret) {
        delete ret.password;
        delete ret.passwordResetToken;
        delete ret.emailVerificationToken;
        return ret;
      },
    },
  }
);

// Indexes
userSchema.index({ email: 1 });
userSchema.index({ userId: 1 });
userSchema.index({ role: 1 });
userSchema.index({ status: 1 });
userSchema.index({ isEmailVerified: 1 });
userSchema.index({ createdAt: -1 });

// Pre-save middleware for password hashing
userSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    (this as any).password = await bcrypt.hash((this as any).password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Instance methods
userSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.generatePasswordResetToken = function (): string {
  const token = nanoid(32);
  this.passwordResetToken = token;
  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  return token;
};

userSchema.methods.generateEmailVerificationToken = function (): string {
  const token = nanoid(32);
  this.emailVerificationToken = token;
  this.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
  return token;
};

userSchema.methods.isLocked = function (): boolean {
  return !!(this.lockUntil && this.lockUntil > new Date());
};

userSchema.methods.incrementLoginAttempts = async function (): Promise<void> {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < new Date()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 },
    });
  }

  const updates: { $inc: { loginAttempts: number }; $set?: { lockUntil: Date } } = {
    $inc: { loginAttempts: 1 },
  };

  // If we're at max attempts and not locked, lock the account
  if (this.loginAttempts + 1 >= 5 && !this.isLocked()) {
    updates.$set = { lockUntil: new Date(Date.now() + 2 * 60 * 60 * 1000) }; // 2 hours
  }

  return this.updateOne(updates);
};

userSchema.methods.resetLoginAttempts = async function (): Promise<void> {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
  });
};

userSchema.methods.getFullName = function (): string {
  return `${this.firstName} ${this.lastName}`;
};

userSchema.methods.toSafeObject = function () {
  const obj = this.toObject();
  delete obj.password;
  delete obj.passwordResetToken;
  delete obj.emailVerificationToken;
  return obj;
};

// Static methods
userSchema.statics.findByEmail = async function (email: string): Promise<IUser | null> {
  return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.findByUserId = async function (userId: string): Promise<IUser | null> {
  return this.findOne({ userId });
};

userSchema.statics.createUser = async function (userData: CreateUserInput): Promise<IUser> {
  // Validate input with Zod
  const validatedData = CreateUserSchema.parse(userData);

  // Check if user already exists
  const existingUser = await this.findOne({ email: validatedData.email.toLowerCase() });
  if (existingUser) {
    throw new Error('User with this email already exists');
  }

  // Create new user
  const user = new this(validatedData);
  await user.save();

  return user as IUser;
};

userSchema.statics.authenticateUser = async function (
  email: string,
  password: string
): Promise<{ user: IUser | null; isValid: boolean }> {
  const user = await this.findOne({ email: email.toLowerCase() });

  if (!user) {
    return { user: null, isValid: false };
  }

  // Check if account is locked
  if (user.isLocked()) {
    throw new Error('Account is temporarily locked due to too many failed login attempts');
  }

  const isValid = await user.comparePassword(password);

  if (isValid) {
    // Reset login attempts on successful login
    if (user.loginAttempts > 0) {
      await user.resetLoginAttempts();
    }

    // Update last login
    user.lastLoginAt = new Date();
    await user.save();

    return { user, isValid: true };
  } else {
    // Increment login attempts on failed login
    await user.incrementLoginAttempts();
    return { user, isValid: false };
  }
};

userSchema.statics.findActiveUsers = async function (): Promise<IUser[]> {
  return this.find({ status: 'active' }).sort({ createdAt: -1 });
};

userSchema.statics.findByRole = async function (role: UserRole): Promise<IUser[]> {
  return this.find({ role }).sort({ createdAt: -1 });
};

// Create and export the model
export const User =
  (mongoose.models.User as IUserModel) || mongoose.model<IUser, IUserModel>('User', userSchema);
