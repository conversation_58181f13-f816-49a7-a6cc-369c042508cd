import mongoose from 'mongoose';

import {
  Account,
  ActivityLog,
  AiInterview,
  Job,
  JobApplication,
  Organization,
  OrganizationMember,
  Otp,
  SubscriptionLog,
  SubscriptionPlan,
  User,
} from './models';

/**
 * Database setup and index management
 * This file contains utilities for setting up the database,
 * creating indexes, and managing database operations
 */

export class DatabaseSetup {
  /**
   * Initialize all database indexes
   * This should be called during application startup
   */
  static async createIndexes(): Promise<void> {
    console.log('🔧 Creating database indexes...');

    try {
      // Create indexes for all models
      await Promise.all([
        User.createIndexes(),
        Otp.createIndexes(),
        Organization.createIndexes(),
        OrganizationMember.createIndexes(),
        Account.createIndexes(),
        ActivityLog.createIndexes(),
        Job.createIndexes(),
        JobApplication.createIndexes(),
        AiInterview.createIndexes(),
        SubscriptionPlan.createIndexes(),
        SubscriptionLog.createIndexes(),
      ]);

      console.log('✅ Database indexes created successfully');
    } catch (error) {
      console.error('❌ Error creating database indexes:', error);
      throw error;
    }
  }

  /**
   * Drop all indexes (useful for development/testing)
   */
  static async dropIndexes(): Promise<void> {
    console.log('🗑️ Dropping database indexes...');

    try {
      const collections = await mongoose.connection.db.collections();

      await Promise.all(
        collections.map(async (collection) => {
          try {
            await collection.dropIndexes();
          } catch (error) {
            // Ignore errors for collections without indexes
            console.warn(`Warning: Could not drop indexes for ${collection.collectionName}`);
          }
        })
      );

      console.log('✅ Database indexes dropped successfully');
    } catch (error) {
      console.error('❌ Error dropping database indexes:', error);
      throw error;
    }
  }

  /**
   * Get database statistics
   */
  static async getDatabaseStats(): Promise<any> {
    try {
      const stats = await mongoose.connection.db.stats();
      const collections = await mongoose.connection.db.collections();

      const collectionStats = await Promise.all(
        collections.map(async (collection) => {
          const collStats = await collection.stats();
          const indexes = await collection.indexes();

          return {
            name: collection.collectionName,
            documents: collStats.count,
            size: collStats.size,
            avgObjSize: collStats.avgObjSize,
            indexes: indexes.length,
            indexSizes: collStats.totalIndexSize,
          };
        })
      );

      return {
        database: {
          name: stats.db,
          collections: stats.collections,
          documents: stats.objects,
          dataSize: stats.dataSize,
          storageSize: stats.storageSize,
          indexSize: stats.indexSize,
        },
        collections: collectionStats,
      };
    } catch (error) {
      console.error('❌ Error getting database stats:', error);
      throw error;
    }
  }

  /**
   * Validate database connection and models
   */
  static async validateDatabase(): Promise<boolean> {
    try {
      console.log('🔍 Validating database connection and models...');

      // Check connection
      if (mongoose.connection.readyState !== 1) {
        throw new Error('Database not connected');
      }

      // Test each model
      const modelTests = [
        { name: 'User', model: User },
        { name: 'Otp', model: Otp },
        { name: 'Organization', model: Organization },
        { name: 'OrganizationMember', model: OrganizationMember },
        { name: 'Account', model: Account },
        { name: 'ActivityLog', model: ActivityLog },
        { name: 'Job', model: Job },
        { name: 'JobApplication', model: JobApplication },
        { name: 'AiInterview', model: AiInterview },
        { name: 'SubscriptionPlan', model: SubscriptionPlan },
        { name: 'SubscriptionLog', model: SubscriptionLog },
      ];

      for (const { name, model } of modelTests) {
        try {
          await model.findOne().limit(1);
          console.log(`✅ ${name} model validated`);
        } catch (error) {
          console.error(`❌ ${name} model validation failed:`, error);
          return false;
        }
      }

      console.log('✅ Database validation completed successfully');
      return true;
    } catch (error) {
      console.error('❌ Database validation failed:', error);
      return false;
    }
  }

  /**
   * Clean up expired data
   */
  static async cleanupExpiredData(): Promise<void> {
    console.log('🧹 Cleaning up expired data...');

    try {
      const now = new Date();

      // Clean up expired OTPs
      const expiredOtps = await Otp.deleteMany({
        expiresAt: { $lt: now },
      });
      console.log(`🗑️ Removed ${expiredOtps.deletedCount} expired OTPs`);

      // Clean up expired AI interviews
      const expiredInterviews = await AiInterview.updateMany(
        {
          expiresAt: { $lt: now },
          status: { $nin: ['completed', 'cancelled', 'expired'] },
        },
        { $set: { status: 'expired' } }
      );
      console.log(`⏰ Marked ${expiredInterviews.modifiedCount} AI interviews as expired`);

      // Clean up old activity logs (older than 1 year)
      const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      const oldLogs = await ActivityLog.deleteMany({
        timestamp: { $lt: oneYearAgo },
        level: { $nin: ['critical', 'error'] }, // Keep critical and error logs
      });
      console.log(`🗑️ Removed ${oldLogs.deletedCount} old activity logs`);

      console.log('✅ Data cleanup completed successfully');
    } catch (error) {
      console.error('❌ Error during data cleanup:', error);
      throw error;
    }
  }

  /**
   * Backup database collections
   */
  static async backupCollections(outputPath: string): Promise<void> {
    console.log(`💾 Starting database backup to ${outputPath}...`);

    try {
      // This is a placeholder for backup functionality
      // In a real implementation, you would use mongodump or similar tools
      console.log('⚠️ Backup functionality not implemented - use mongodump for production backups');

      // Example backup command that should be run externally:
      // mongodump --uri="mongodb://localhost:27017/interview-app" --out="/path/to/backup"
    } catch (error) {
      console.error('❌ Error during backup:', error);
      throw error;
    }
  }

  /**
   * Initialize database for development
   */
  static async initializeDevelopment(): Promise<void> {
    console.log('🚀 Initializing database for development...');

    try {
      // Create indexes
      await this.createIndexes();

      // Validate database
      await this.validateDatabase();

      // Create default subscription plans if they don't exist
      await this.createDefaultSubscriptionPlans();

      console.log('✅ Development database initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing development database:', error);
      throw error;
    }
  }

  /**
   * Create default subscription plans
   */
  static async createDefaultSubscriptionPlans(): Promise<void> {
    try {
      const existingPlans = await SubscriptionPlan.countDocuments();

      if (existingPlans === 0) {
        console.log('📋 Creating default subscription plans...');

        const defaultPlans = [
          {
            name: 'Free',
            description: 'Perfect for getting started',
            type: 'free' as const,
            pricing: [{ amount: 0, currency: 'USD', interval: 'monthly' as const }],
            features: {
              maxJobs: 3,
              maxApplicationsPerJob: 50,
              maxOrganizationMembers: 2,
              maxAiInterviewsPerMonth: 10,
              maxCandidatesPerMonth: 50,
              aiInterviewEnabled: true,
              aiAnalysisEnabled: false,
              aiRecommendationsEnabled: false,
              customAiQuestions: false,
              customBranding: false,
              advancedAnalytics: false,
              apiAccess: false,
              ssoIntegration: false,
              prioritySupport: false,
              dedicatedAccountManager: false,
              atsIntegration: false,
              slackIntegration: false,
              teamsIntegration: false,
              webhooksEnabled: false,
              dataRetentionMonths: 6,
              dataExportEnabled: false,
              bulkOperations: false,
              customLimits: [],
            },
            trialDays: 14,
            sortOrder: 1,
          },
          {
            name: 'Professional',
            description: 'For growing teams',
            type: 'professional' as const,
            pricing: [
              { amount: 49, currency: 'USD', interval: 'monthly' as const },
              { amount: 490, currency: 'USD', interval: 'yearly' as const },
            ],
            features: {
              maxJobs: 25,
              maxApplicationsPerJob: 500,
              maxOrganizationMembers: 10,
              maxAiInterviewsPerMonth: 100,
              maxCandidatesPerMonth: 500,
              aiInterviewEnabled: true,
              aiAnalysisEnabled: true,
              aiRecommendationsEnabled: true,
              customAiQuestions: true,
              customBranding: true,
              advancedAnalytics: true,
              apiAccess: true,
              ssoIntegration: false,
              prioritySupport: true,
              dedicatedAccountManager: false,
              atsIntegration: true,
              slackIntegration: true,
              teamsIntegration: true,
              webhooksEnabled: true,
              dataRetentionMonths: 24,
              dataExportEnabled: true,
              bulkOperations: true,
              customLimits: [],
            },
            isPopular: true,
            sortOrder: 2,
          },
          {
            name: 'Enterprise',
            description: 'For large organizations',
            type: 'enterprise' as const,
            pricing: [
              { amount: 199, currency: 'USD', interval: 'monthly' as const },
              { amount: 1990, currency: 'USD', interval: 'yearly' as const },
            ],
            features: {
              maxJobs: -1, // unlimited
              maxApplicationsPerJob: -1,
              maxOrganizationMembers: -1,
              maxAiInterviewsPerMonth: -1,
              maxCandidatesPerMonth: -1,
              aiInterviewEnabled: true,
              aiAnalysisEnabled: true,
              aiRecommendationsEnabled: true,
              customAiQuestions: true,
              customBranding: true,
              advancedAnalytics: true,
              apiAccess: true,
              ssoIntegration: true,
              prioritySupport: true,
              dedicatedAccountManager: true,
              atsIntegration: true,
              slackIntegration: true,
              teamsIntegration: true,
              webhooksEnabled: true,
              dataRetentionMonths: -1, // unlimited
              dataExportEnabled: true,
              bulkOperations: true,
              customLimits: [],
            },
            sortOrder: 3,
          },
        ];

        await Promise.all(defaultPlans.map((plan) => SubscriptionPlan.create(plan)));

        console.log('✅ Default subscription plans created');
      }
    } catch (error) {
      console.error('❌ Error creating default subscription plans:', error);
      throw error;
    }
  }
}

// Export utility functions
export const dbUtils = {
  createIndexes: DatabaseSetup.createIndexes,
  validateDatabase: DatabaseSetup.validateDatabase,
  cleanupExpiredData: DatabaseSetup.cleanupExpiredData,
  getDatabaseStats: DatabaseSetup.getDatabaseStats,
  initializeDevelopment: DatabaseSetup.initializeDevelopment,
};

// Export all models for convenience
export {
  Account,
  ActivityLog,
  AiInterview,
  Job,
  JobApplication,
  Organization,
  OrganizationMember,
  Otp,
  SubscriptionLog,
  SubscriptionPlan,
  User,
};

// Export all types
export * from './models';
