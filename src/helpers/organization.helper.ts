import { z } from 'zod';

import type {
  CreateOrganizationInput,
  Industry,
  IOrganization,
  OrganizationType,
  UpdateOrganizationInput,
} from '/@types';

import { Organization } from '../db/models/organization.model';

/**
 * Organization Service Helper for Next.js API routes
 * Provides easy-to-use methods for organization management operations
 */
export class OrganizationService {
  /**
   * Create a new organization
   */
  static async createOrganization(orgData: CreateOrganizationInput): Promise<IOrganization> {
    try {
      return await Organization.createOrganization(orgData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map((e) => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  /**
   * Get organization by ID
   */
  static async getOrganizationById(organizationId: string): Promise<IOrganization | null> {
    return await Organization.findByOrganizationId(organizationId);
  }

  /**
   * Get organization by slug
   */
  static async getOrganizationBySlug(slug: string): Promise<IOrganization | null> {
    return await Organization.findBySlug(slug);
  }

  /**
   * Update organization
   */
  static async updateOrganization(
    organizationId: string,
    updateData: UpdateOrganizationInput
  ): Promise<IOrganization | null> {
    return await Organization.findOneAndUpdate(
      { organizationId },
      { $set: updateData },
      { new: true, runValidators: true }
    );
  }

  /**
   * Delete organization
   */
  static async deleteOrganization(organizationId: string): Promise<boolean> {
    const result = await Organization.deleteOne({ organizationId });
    return result.deletedCount === 1;
  }

  /**
   * Get organizations by owner
   */
  static async getOrganizationsByOwner(ownerId: string): Promise<IOrganization[]> {
    return await Organization.findByOwner(ownerId);
  }

  /**
   * Get organizations by industry
   */
  static async getOrganizationsByIndustry(industry: Industry): Promise<IOrganization[]> {
    return await Organization.findByIndustry(industry);
  }

  /**
   * Search organizations
   */
  static async searchOrganizations(query: string): Promise<IOrganization[]> {
    return await Organization.searchOrganizations(query);
  }

  /**
   * Get active organizations
   */
  static async getActiveOrganizations(): Promise<IOrganization[]> {
    return await Organization.findActiveOrganizations();
  }

  /**
   * Verify organization
   */
  static async verifyOrganization(organizationId: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const organization = await Organization.findByOrganizationId(organizationId);
    if (!organization) {
      return { success: false, message: 'Organization not found' };
    }

    organization.isVerified = true;
    organization.verifiedAt = new Date();
    await organization.save();

    return { success: true, message: 'Organization verified successfully' };
  }

  /**
   * Suspend organization
   */
  static async suspendOrganization(
    organizationId: string,
    reason?: string
  ): Promise<{ success: boolean; message: string }> {
    const result = await Organization.findOneAndUpdate(
      { organizationId },
      {
        $set: {
          status: 'suspended',
          suspendedAt: new Date(),
          suspensionReason: reason,
        },
      },
      { new: true }
    );

    return {
      success: !!result,
      message: result ? 'Organization suspended successfully' : 'Organization not found',
    };
  }

  /**
   * Reactivate organization
   */
  static async reactivateOrganization(organizationId: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const result = await Organization.findOneAndUpdate(
      { organizationId },
      {
        $set: { status: 'active' },
        $unset: { suspendedAt: 1, suspensionReason: 1 },
      },
      { new: true }
    );

    return {
      success: !!result,
      message: result ? 'Organization reactivated successfully' : 'Organization not found',
    };
  }

  /**
   * Update organization settings
   */
  static async updateSettings(
    organizationId: string,
    settings: any
  ): Promise<IOrganization | null> {
    const organization = await Organization.findByOrganizationId(organizationId);
    if (!organization) return null;

    await organization.updateSettings(settings);
    return organization;
  }

  /**
   * Get organization statistics
   */
  static async getOrganizationStats(): Promise<{
    total: number;
    active: number;
    verified: number;
    suspended: number;
    byType: Record<OrganizationType, number>;
    byIndustry: Record<Industry, number>;
    bySize: Record<string, number>;
  }> {
    const [total, active, verified, suspended, byType, byIndustry, bySize] = await Promise.all([
      Organization.countDocuments(),
      Organization.countDocuments({ status: 'active' }),
      Organization.countDocuments({ isVerified: true }),
      Organization.countDocuments({ status: 'suspended' }),
      Organization.aggregate([
        { $group: { _id: '$type', count: { $sum: 1 } } },
        { $group: { _id: null, types: { $push: { type: '$_id', count: '$count' } } } },
      ]),
      Organization.aggregate([
        { $group: { _id: '$industry', count: { $sum: 1 } } },
        { $group: { _id: null, industries: { $push: { industry: '$_id', count: '$count' } } } },
      ]),
      Organization.aggregate([
        { $group: { _id: '$size', count: { $sum: 1 } } },
        { $group: { _id: null, sizes: { $push: { size: '$_id', count: '$count' } } } },
      ]),
    ]);

    const typeStats =
      byType[0]?.types.reduce((acc: any, item: any) => {
        acc[item.type] = item.count;
        return acc;
      }, {}) || {};

    const industryStats =
      byIndustry[0]?.industries.reduce((acc: any, item: any) => {
        acc[item.industry] = item.count;
        return acc;
      }, {}) || {};

    const sizeStats =
      bySize[0]?.sizes.reduce((acc: any, item: any) => {
        acc[item.size] = item.count;
        return acc;
      }, {}) || {};

    return {
      total,
      active,
      verified,
      suspended,
      byType: typeStats,
      byIndustry: industryStats,
      bySize: sizeStats,
    };
  }

  /**
   * Get organization's public profile
   */
  static async getPublicProfile(organizationId: string): Promise<Partial<IOrganization> | null> {
    const organization = await Organization.findByOrganizationId(organizationId);
    return organization ? organization.getPublicProfile() : null;
  }

  /**
   * Check slug availability
   */
  static async isSlugAvailable(slug: string, excludeOrgId?: string): Promise<boolean> {
    const query: any = { slug };
    if (excludeOrgId) {
      query.organizationId = { $ne: excludeOrgId };
    }

    const existing = await Organization.findOne(query);
    return !existing;
  }

  /**
   * Generate unique slug
   */
  static async generateUniqueSlug(baseName: string): Promise<string> {
    const slug = baseName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    let counter = 0;
    let uniqueSlug = slug;

    while (!(await this.isSlugAvailable(uniqueSlug))) {
      counter++;
      uniqueSlug = `${slug}-${counter}`;
    }

    return uniqueSlug;
  }

  /**
   * Update member and job counts
   */
  static async updateCounts(organizationId: string): Promise<{
    memberCount: number;
    jobCount: number;
  }> {
    const organization = await Organization.findByOrganizationId(organizationId);
    if (!organization) {
      throw new Error('Organization not found');
    }

    const memberCount = await organization.updateMemberCount();
    const jobCount = await organization.updateJobCount();

    return { memberCount, jobCount };
  }

  /**
   * Get organizations with pagination
   */
  static async getOrganizationsPaginated(options: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    filters?: any;
  }): Promise<{
    organizations: IOrganization[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      filters = {},
    } = options;

    const skip = (page - 1) * limit;
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const [organizations, total] = await Promise.all([
      Organization.find(filters).sort(sort).skip(skip).limit(limit),
      Organization.countDocuments(filters),
    ]);

    const pages = Math.ceil(total / limit);

    return {
      organizations,
      pagination: {
        page,
        limit,
        total,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Export organizations data
   */
  static async exportOrganizations(filters?: any): Promise<Partial<IOrganization>[]> {
    const organizations = await Organization.find(filters || {});
    return organizations.map((org) => org.toObject());
  }
}
