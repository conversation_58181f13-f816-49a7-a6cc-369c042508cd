'use client';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { useAuth } from './use-auth';

export function useAuthGuard(redirectPath = '/login') {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push(redirectPath);
    }
  }, [user, loading, router, redirectPath]);

  return { user, loading, isAuthenticated: !!user };
}

export function useRoleGuard(requiredRole: 'user' | 'admin', redirectPath = '/unauthorized') {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.push('/login');
      } else if (requiredRole === 'admin' && user.role !== 'admin') {
        router.push(redirectPath);
      }
    }
  }, [user, loading, router, requiredRole, redirectPath]);

  return {
    user,
    loading,
    hasAccess: user?.role === requiredRole || (requiredRole === 'user' && !!user),
    isAuthenticated: !!user,
  };
}

export function useGuestGuard(redirectPath = '/dashboard') {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user) {
      router.push(redirectPath);
    }
  }, [user, loading, router, redirectPath]);

  return { user, loading, isGuest: !user };
}
